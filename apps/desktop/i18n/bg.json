{"BUTTONS": {"PAY": "Pay", "ADD_EXISTING_USER": "Add Existing User", "ADD_NEW": "Add New", "ADD": "Добавяне", "CREATE": "Създаване", "REGISTER": "Регистриране", "LOGIN": "Log In", "ADD_NOTE": "Добави бележка", "EDIT": "Редактиране", "MANAGE": "Управление", "DETAILS": "Детайли", "DUPLICATE": "Дублиране", "DELETE": "Изтриване", "REMOVE": "Remove", "ADD_EXISTING": "Add Existing", "OK": "Да", "YES": "Yes", "NO": "No", "SAVE": "Запази", "CLEAR_ALL": "Clear All", "BACK": "Обратно", "SENT": "Изпратено", "ACCEPTED": "Прието", "MARK_AS_SENT": "<PERSON> as <PERSON><PERSON>", "MARK_AS_ACCEPTED": "<PERSON> as Accepted", "CANCEL": "Отказ", "CLOSE": "Затваряне", "INVITE": "Покана", "SELECT_ALL": "Селектиране на всички", "COPY_LINK": "Копирай линка", "MANAGE_INTERVIEWS": "Manage Interviews", "MANAGE_INVITES": "Manage Invites", "MANAGE_SPRINTS": "Manage Sprints", "RESEND": "Преизпрати", "NEXT": "Следващ", "PREVIOUS": "Предишен", "INVITE_AGAIN": "Invite Again", "REQUEST": "Request", "HISTORY": "History", "SYNC": "Sync", "UPDATE": "Update", "AUTO_SYNC": "Auto sync", "VIEW": "View", "SEND": "Send", "ARCHIVE": "Archive", "HIRE": "<PERSON>re", "MANAGE_CATEGORIES": "Manage Categories", "REJECT": "Reject", "FIND_TIME": "Find time", "DOWNLOAD": "Download", "ADD_KNOWLEDGE_BASE": "Add Knowledge Base", "CHOOSE_ICON": "Choose icon", "MAKE_PRIVATE": "make private", "MAKE_PUBLIC": "make public", "KNOWLEDGE_BASES": "Knowledge bases", "SELECT": "Select", "EMAIL": "Eлектронна поща", "CONVERT_TO_INVOICE": "Convert to invoice", "TO_INVOICE": "To invoice", "PUBLIC_APPOINTMENT_BOOK": "Book Public Appointment", "SAVE_AS_DRAFT": "Save as Draft", "SAVE_AND_SEND_CONTACT": "Save and send to contact in gauzy", "SAVE_AND_SEND_EMAIL": "Save and send via email", "EVENT_TYPES": "Event Types", "SEARCH": "Search", "RESET": "Reset", "LEAVE_FEEDBACK": "Leave a feedback", "SPRINT": {"CREATE": "Create Sprint", "EDIT": "Редактиране", "DELETE": "Изтрий"}, "CANDIDATE_STATISTIC": "Statistic", "FILTER": "Filter", "REFRESH": "Refresh", "AUTO_REFRESH": "Auto Refresh", "PROPOSAL_DELETE_MESSAGE": "Proposal template successfully delete", "PROPOSAL_MAKE_DEFAULT_MESSAGE": "Proposal template successfully make as default", "MANAGE_TEMPLATES": "Manage Templates", "MAKE_DEFAULT": "Make Default", "HIDE_ALL": "Hide All", "SCHEDULES": "Schedules", "YES_HIDE_ALL_JOBS": "Yes, Hide All Jobs", "VALIDATE": "Validate", "VALIDATED": "Validated", "APPROVE": "Approve", "DENY": "<PERSON><PERSON>", "PAYMENTS": "Payments", "NOTE": "Note", "SKIP_CONTINUE": "Skip {{ label }} and Continue", "BUY": "Buy", "DELETE_ACCOUNT": "Delete your account", "DELETE_ALL_DATA": "Delete all data", "SELECT_AND_CONTINUE": "Select and Continue", "ADD_KPI": "Add KPI", "PAID_DAYS_OFF": "Paid days off", "UNPAID_DAYS_OFF": "Unpaid days off", "CLEAR": "Clear", "SET": "Set", "RECORD_FULL_PAYMENT": "Record Full Payment", "EXPORT_TO_CSV": "Export to CSV", "INVOICE_REMAINING_AMOUNT": "Invoice Remaining Amount", "PUBLIC_LINK": "Public Link", "GENERATE": "Generate", "SEND_RECEIPT": "Send Receipt", "ADD_COMMENT": "Add Comment", "CONTINUE": "Continue", "SUPER_ADMIN_DEMO": "Super Admin Demo", "ADMIN_DEMO": "<PERSON><PERSON>", "EMPLOYEE_DEMO": "Employee Demo", "DEMO_CREDENTIALS": "Demo Credentials", "CREATE_NEW_ROLE": "Create {{ name }} Role", "DELETE_EXISTING_ROLE": "Delete {{ name }} Role", "RESTORE": "Rest<PERSON>", "VIEW_ALL": "View All", "VIEW_REPORT": "View Report", "TIME_TRACKING_ENABLE": "Enable Time Tracking", "TIME_TRACKING_DISABLE": "Disable Time Tracking", "PRINT": "Print", "FEEDBACK": "<PERSON><PERSON><PERSON>", "EQUIPMENT_SHARING": "Equipment Sharing", "PRIVATE": "Private", "PUBLIC": "Public", "MANAGE_WIDGET": "Manage widgets", "MOVE": "Move", "COLLAPSE": "Collapse", "EXPAND": "Expand", "SHOW_MORE": "Show More", "START_WORK": "Start Work", "APPLY": "Прило<PERSON>и", "GENERATE_PROPOSAL": "Генериране на предложение", "LET_S_GO": "Нека отидем", "CHECK": "Проверка", "CHECK_UPDATE": "Провери актуализацията", "DOWNLOAD_NOW": "Изтегли сега", "UPDATE_NOW": "Актуализи<PERSON><PERSON>й сега", "SAVE_RESTART": "Запази и рестартирай", "FILES": "Файлове", "START": "Старт", "STOP": "Стоп", "ACKNOWLEDGE": "При<PERSON><PERSON><PERSON>", "RESTART": "<PERSON>ест<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LATER": "По-късно", "UPGRADE": "Обнови", "SKIP_NOW": "Пропусни сега", "EXIT": "Изход", "LOGOUT": "Изход", "COPY": "Копиране", "CUT": "Изрязване", "PASTE": "Поставяне", "FORWARD_PORTS": "Препращане на портове", "REPORT": "Доклад", "IGNORE": "Игнориране"}, "SM_TABLE": {"NO_DATA": {"RECEIVE_ESTIMATE": "You have not received any estimate.", "INCOME": "You have not created any income.", "EXPENSE_CATEGORY": "You have not created any expense category.", "REPORT": "You have not created any report.", "CONTRACT": "You have not created any contract.", "TEAM": "You have not created any team.", "HISTORY_RECORD": "You have not created any record.", "PROFIT_HISTORY": "You have not created any profit history.", "EMPLOYEE": "You have not created any employee.", "EXPENSE": "You have not created any expense.", "PAYMENT": "You have not received any payment.", "PROPOSAL_TEMPLATE": "You have not created any proposal template.", "PROPOSAL": "You have not created any proposal.", "PIPELINE": "You have not created any pipeline.", "TASK": "You have not created any task.", "INVITE": "You have not invited any user.", "APPROVAL_REQUEST": "Не сте създали заявка за одобрение.", "APPROVAL_POLICY": "You have not created any approval policy.", "TIME_OFF": "You have not created any time off.", "CANDIDATE": "You have not created any candidate.", "INTERVIEW": "You have not created any interview.", "EQUIPMENT": "You have not created any equipment.", "EQUIPMENT_SHARING": "You have not created any equipment sharing.", "EQUIPMENT_SHARING_POLICY": "You have not created any equipment sharing policy.", "INVENTORY": "You have not created any inventory.", "MERCHANT": "You have not created any merchant.", "WAREHOUSE": "You have not created any warehouse.", "WAREHOUSE_PRODUCT": "No warehouse products here.", "PRODUCT_CATEGORY": "You have not created any product category.", "TAGS": "You have not created any tag.", "PROJECT": "You have not created any project.", "DEPARTMENT": "You have not created any department.", "CONTACT": "You have not created any contact.", "CLIENT": "You have not created any client.", "LEAD": "You have not created any lead.", "TIME_FRAME": "You have not created any time frame.", "KPI": "You have not created any KPI.", "INVOICE": "You have not created any invoice.", "ESTIMATE": "You have not created any estimate.", "EVENT_TYPE": "You have not created any event type.", "PRODUCT_TYPE_NO_DATA": "You have not created any product type.", "TEAM_DASHBOARD": "You don't have any teams"}, "TRANSACTION_TYPE": "Тип", "AMOUNT": "Сума", "DATE": "Дата", "TITLE": "Title", "STAGE": "Stage", "START_DATE": "Начална дата", "END_DATE": "Крайна дата", "CLIENT_NAME": "Клиентско име", "CONTACT_NAME": "Contact Name", "NAME": "Име", "VENDOR": "Доставчик", "CATEGORY": "Категория", "CURRENCY": "Валута", "VALUE": "Стойност", "NOTES": "Бележки", "EMPLOYEE": "Служител", "EMPLOYEES": "Служители", "FULL_NAME": "Две имена", "EMAIL": "Eлектронна поща", "INCOME": "Доход (Средно)", "EXPENSES": "Разходи (Средно)", "BONUS": "<PERSON>о<PERSON><PERSON><PERSON>", "BONUS_AVG": "Бон<PERSON><PERSON> (Средно)", "PROFIT_BASED_BONUS": "Бонус въз основа на печалбата", "REVENUE_BASED_BONUS": "Бонус въз основа на приходите", "STATUS": "Статус", "SOURCE": "Source", "WORK_STATUS": "Работен статус", "TODAY": "<PERSON><PERSON><PERSON><PERSON>", "END_OF_MONTH": "В края на месеца", "START_OF_MONTH": "В началото на месеца", "RATE": "Почасово", "FLAT_FEE": "Фиксир<PERSON>на тарифа", "MILESTONES": "Поетапно", "JOB_TITLE": "Заглавие на публикацията", "JOB_POST_URL": "Job Post URL", "LINK_TO_JOBPOST": "Връзка към публикацията", "AUTHOR": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MONDAY": "Понеделик", "TUESDAY": "Вторник", "WEDNESDAY": "Сряда", "THURSDAY": "Четвъртък", "FRIDAY": "Петък", "SATURDAY": "Събота", "SUNDAY": "Неделя", "NONE": "Нито един", "ROLE": "Роля", "PROJECTS": "Проекти", "PROJECT": "Проект", "INVITED_BY": "Поканен от", "EXPIRE_DATE": "Изтича", "CLIENTS": "Клиенти", "CONTACTS": "Contacts", "CONTACT": "Contact", "DEPARTMENTS": "Отдели", "DESCRIPTION": "Описание", "POLICY": "Policy", "APPLIED": "Applied", "HIRED": "<PERSON><PERSON>", "REJECTED": "Rejected", "NO_RESULT": "No Result", "CLIENT": "Кли<PERSON><PERSON>т", "INTERNAL": "Internal", "START": "Start", "END": "End", "REQUEST_DATE": "Request Date", "REGION": {"BG": "Български (България)", "EN": "English (United States)", "RU": "Русский (Россия)", "HE": "עברית ישראל", "FR": "<PERSON><PERSON><PERSON> (France)", "ES": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)", "ZH": "中文 (中国)", "DE": "Deutsch (Deutschland)", "PT": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)", "IT": "Italiano (Italia)", "NL": "Nederlands (Nederland)", "PL": "Polski (Polska)", "AR": "العربية (العراق)"}, "CURRENT_VALUE": "Current value", "TARGET_VALUE": "Target value", "LAST_UPDATED": "Last Updated", "CREATED_BY": "Created By", "NO_DATA_MESSAGE": "No Data", "TAGS": "Тагове", "CREATED": "Created", "APPLIED_DATE": "Applied Date", "HIRED_DATE": "<PERSON><PERSON>", "REJECTED_DATE": "Rejected Date", "TIME_TRACKING": "Time Tracking", "CREATED_AT": "Created At"}, "FORM": {"USERNAME": "Потребителско име", "PASSWORD": "Парола", "CONFIRM": "Потвърждение", "FILTER": "Filter", "EMAIL": "Eлектронна поща", "LABELS": {"NAME": "Име", "PHONE_NUMBER": "телефонен номер", "WEBSITE": "Website", "FIRST_NAME": "Име (незадължително)", "LAST_NAME": "Фамилия (незадължително)", "FROM": "From", "TO": "To", "EMPLOYEE": "Служител", "START_DATE": "Date when started work (optional)", "APPLIED_DATE_LABEL": "Date when applied", "IMAGE_URL": "Снимка URL (незадължително)", "CV_URL": "CV URL (optional)", "DOCUMENT_URL": "Document URL", "CURRENCY": "Валута", "DATE_TYPE": "Дата по подразбиране", "ADD_TEAM": "Добавяне на нов отбор", "EDIT_TEAM": "Edit Team", "OFFICIAL_NAME": "Официа<PERSON><PERSON><PERSON> Име", "PROFILE_LINK": "Profile Link", "START_WEEK_ON": "Започни седмицата на", "TAX_ID": "Таксa ID", "TIME_FORMAT": "Time Format", "COUNTRY": "Държава", "CITY": "<PERSON>р<PERSON><PERSON>", "ADDRESS": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ADDRESS_2": "Адрес 2", "LOGO_ALIGNMENT": "Подравняване на логото", "BRAND_COLOR": "Гла<PERSON><PERSON>н Цвят", "DATE_FORMAT": "Формат на Датата", "CHOOSE_TIME_ZONE": "Избери Времева Зона", "START_TIME": "Default Work Day Start Time", "END_TIME": "Default Work Day End Time", "POSTCODE": "Пощенски код (Zip)", "PAY_PERIOD": "Период на Плащане", "BILL_RATE": "Таксуване (на час)", "CURRENCY_PER_HOUR": "Валута", "RECURRING_WEEKLY_LIMIT": "Седмичен лимит (часове)", "ROLE": "Роля", "SOURCE": "Source (optional)", "EMAILS": "Електронни пощи", "PROJECTS_OPTIONAL": "Проекти (по избор)", "CONTACTS_OPTIONAL": "Contacts (Optional)", "DEPARTMENTS_OPTIONAL": "Отдели (по избор)", "TEAMS_OPTIONAL": "Teams (Optional)", "PROJECTS": "Проекти", "ADD_NEW_DEPARTMENT": "Доба<PERSON><PERSON> Нов Отдел", "EDIT_DEPARTMENT": "Редакти<PERSON><PERSON><PERSON> Отдела", "TYPE_OF_BONUS": "Employee Bonus Type", "BONUS_PERCENTAGE": "Процентен Бонус", "ENABLE_DISABLE_INVITES": "Включване / Изключване на Поканите", "ALLOW_USER_INVITES": "Позволи на юзърите да изпращат покани", "INVITE_EXPIRY_PERIOD": "Период на изтичане на поканите (в дни)", "EMPLOYMENT_TYPES": "Employment Type", "ADD_NEW_EMPLOYMENT_TYPE": "Add New Employment Type", "OFFER_DATE": "Offer Date (optional)", "ACCEPT_DATE": "Дата на приемане (опция)", "APPLIED_DATE": "Applied Date (optional)", "EDUCATION": "Education (optional)", "EXPERIENCE": "Work experience (optional)", "SKILLS": "Skills (optional)", "HIRED_DATE": "Hired Date (optional)", "REJECT_DATE": "Дата на отказ (опция)", "DOCUMENT_NAME": "Document name", "FEEDBACK_DESCRIPTION": "Feedback description", "EMAIL_INVITATION": "Enter email to send invitation", "SELECT_EQUIPMENT": "Select equipment", "SELECT_SHARE_REQUEST_DATE": "Select request date", "SELECT_SHARE_START_DATE": "Select start date", "SELECT_SHARE_END_DATE": "Select end date", "SELECT_EMPLOYEE": "Select employee", "SELECT_TEAM": "Select team", "ENABLE_DISABLE_FUTURE_DATE": "Enable/Disable future dates", "ALLOW_FUTURE_DATE": "Allow switching to future periods", "REGISTRATION_DATE": "Registration Date", "ORGANIZATION_NAME": "Organization Name", "MEETING_AGENDA": "Agenda", "MEETING_LOCATION": "Локация", "MEETING_DESCRIPTION": "Description", "MEETING_INVITEES": "Invitees", "TITLE": "Title", "DATE": "Дата", "TIME": "Time", "DURATION": "Duration", "CANDIDATE": "Candidate", "INTERVIEWERS": "Interviewers", "LOCATION": "Локация", "NOTE": "Note", "PREFERRED_LANGUAGE": "Preferred Language", "DESCRIPTION": "Description", "DESCRIPTION_OPTIONAL": "Description (optional)", "ADD_OR_REMOVE_EMPLOYEES": "Add or remove employees", "ADD_REMOVE_MANAGERS": "Add or Remove Managers", "ADD_REMOVE_MEMBERS": "Add or Remove Members", "SHORT_DESCRIPTION": "Short Description", "ENABLE_EMPLOYEE_FEATURES": "Enable Employee Features", "REVOKE_EMPLOYEE_FEATURES": "Revoke Employee Features", "STATUS": "Статус", "FISCAL_YEAR_START_DATE": "Fiscal Year Start Date", "FISCAL_YEAR_END_DATE": "Fiscal Year End Date", "TAX_AND_DISCOUNT_INVOICE_ITEMS_SEPARATELY": "Tax And Discount Invoice Items Separately", "ALLOW_TAXING_AND_DISCOUNTING_OF_INVOICE_ITEMS_SEPARATELY": "Allow taxing and discounting of invoice items separately", "DISCOUNT_AFTER_TAX": "Discount after tax", "APPLY_DISCOUNT_AFTER_TAX_FOR_INVOICES_AND_ESTIMATES": "Apply discount after tax for invoices and estimates", "FIND_ADDRESS": "Find Address", "LINKEDIN": "LinkedIn", "FACEBOOK": "Facebook", "INSTAGRAM": "Instagram", "TWITTER": "Twitter", "GITHUB": "<PERSON><PERSON><PERSON>", "GITLAB": "Gitlab", "UPWORK": "Upwork", "STACK_OVERFLOW": "Stackoverflow", "PROJECT_URL": "Project URL", "CLIENTS": "Клиенти", "IS_PROJECT_OPEN_SOURCE": "Is Project Open-Source", "OPEN_SOURCE_PROJECT_URL": "Open-Source Project URL", "EMPLOYEE_LEVEL": "Employee Level", "UNIT": "Unit", "SELECT_EXISTING_OBJECTIVE": "Select Existing Objective", "LENGTH": "Length", "DATE_START": "Start date", "END_DATE": "Крайна дата", "GOAL": "Goal", "DOWNLOAD_REQUEST_FORM": "Download Request Form", "COORDINATE": {"TITLE": "COORDINATES", "LATITUDE": "Latitude", "LONGITUDE": "Longitude"}, "PUBLIC_LINK": "Public Link", "DEFAULT_TERMS": "Default terms for invoices and estimates", "CONVERT_ESTIMATES": "Convert Estimates", "ALLOW_CONVERTING": "Automatically convert accepted estimate to invoice", "DEFAULT_DAYS": "Default days until invoices and estimates are due.", "TEMPLATE_NAME": "Template Name", "TEMPLATE_BODY": "Template Body", "TEMPLATE_PREVIEW": "Template Preview", "LANGUAGE": "<PERSON>зи<PERSON> (Language)", "DEFAULT_INVOICE_TEMPLATE": "Default Invoice Template", "DEFAULT_ESTIMATE_TEMPLATE": "Default Estimate Template", "DEFAULT_RECEIPT_TEMPLATE": "Default Receipt Template", "DEFAULT": "Default Organization", "INVITATION_EXPIRATION": "Invitation Expiration", "PERIOD": "Period", "REGISTER_AS_EMPLOYEE_OF_ORGANIZATION": "Do you want to register as Employee of Organization?", "COVER_LETTER": "Cover Letter", "DETAILS": "Details", "HOURLY_RATE": "Hourly Rate", "ATTACHMENTS": "Attachments", "UPWORK_ORGANIZATION_ID": "Upwork Organization ID", "UPWORK_ORGANIZATION_NAME": "Upwork Organization Name", "UPWORK_ID": "Upwork ID", "LINKEDIN_ID": "LinkedIn ID"}, "PLACEHOLDERS": {"NAME": "Име", "PHONE_NUMBER": "телефонен номер", "DEFAULT": "Select Default", "FIRST_NAME": "Име", "LAST_NAME": "Фамилия", "COMPANY_NAME": "Име на компания", "ALL_EMPLOYEES": "Всички служители", "CURRENCY": "Валута", "ALL_CURRENCIES": "Всички валути", "ALL_DEPARTMENTS": "Всички отдели", "ALL_POSITIONS": "Всички позиции", "START_DATE": "Дата", "PICK_DATE": "Изберете дата", "DATE_TYPE": "Всички типове дати", "BILLING": "Billing", "BILLABLE": "Billable", "CODE": "Code", "COLOR": "Color", "WEBSITE": "Website", "CURRENCY_POSITION": "Currency Position", "FISCAL_INFORMATION": "Fiscal Information", "IMAGE_URL": "Снимка", "ADD_DEPARTMENT": "Добави Отдел", "ADD_POSITION": "Добави позиция", "ADD_VENDOR": "Добави доставчик", "ADD_SKILL": "Add skill", "ADD_CANDIDATE_QUALITY": "Add personal quality", "ADD_TECHNOLOGY": "Add technology", "ADD_EXPENSE_CATEGORY": "Add expense category", "CONTACTS": "Contacts", "START_DATE_PROJECT": "Project Start Date", "END_DATE_PROJECT": "Project End Date", "TEAM_NAME": "Име на отбора", "ADD_REMOVE_MEMBERS": "Добавяне или премахване на членове", "ADD_REMOVE_MANAGERS": "Add or Remove Team Managers", "MEMBERS_COUNT": "Брой членове", "OFFICIAL_NAME": "Добави официално име", "PROFILE_LINK": "Enter Profile Link", "START_WEEK_ON": "Започни седмицата на", "TAX_ID": "Такса ID", "COUNTRY": "Държава", "CITY": "<PERSON>р<PERSON><PERSON>", "ADDRESS": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ADDRESS_2": "Адрес 2", "EDUCATION": "Education", "EXPERIENCE": "Work experience", "SKILLS": "Skills", "POSTCODE": "Пощенски код (Zip)", "BILL_RATE": "Заплащане на час", "RECURRING_WEEKLY_LIMIT": "Седмичен лимит (часове)", "EMAILS": "Въведи електронна поща и натисни \"Enter\"", "ROLE": "Избери роля", "PROJECTS": "Избери проекти", "CHOOSE_FORMAT": "Избери формат", "CHOOSE_TIME_ZONE": "Избери времева зона", "START_TIME": "HH:mm", "END_TIME": "HH:mm", "ADD_COLOR": "Добави цвят", "ALIGN_LOGO_TO": "Подравни логото към", "DEPARTMENTS": "Отдели", "TEAMS": "Отбори", "NUMBER_FORMAT": "Избери формат на числото", "REGIONS": "Избери област", "REMOVE_IMAGE": "Премахни снимка", "UPLOADER_PLACEHOLDER": "Снимка", "UPLOADER_DOCUMENT_PLACEHOLDER": "URL", "ADD_REMOVE_PROJECTS": "Добавяне или премахване на проекти", "ADD_REMOVE_TEAMS": "Добавяне или премахване на екипи", "ADD_REMOVE_EMPLOYEES": "Добави или премахни служители", "ADD_REMOVE_CANDIDATE": "Add Candidate", "ADD_REMOVE_EMPLOYEE": "Add Interviewer", "ADD_REMOVE_INTERVIEWER": "Select interviewer", "ADD_REMOVE_CANDIDATES": "Add or Remove Candidates", "ADD_REMOVE_USERS": "Add or Remove Users", "ADD_REMOVE_ORGANIZATIONS": "Add or Remove Organizations", "ADD_ORGANIZATIONS": "Add Organizations", "DATE": "Дата", "VALUE": "Стойност", "SELECT_CURRENCY": "Избери валута", "TYPE_OF_BONUS": "Select Type of Bonus", "BONUS_PERCENTAGE": "Бонус процентен", "ENABLE_INVITES": "Позволи поканите", "INVITE_EXPIRY_PERIOD": "Покани валидни до", "SWITCH_PROJECT_STATE": "Public", "CHOOSE_EMPLOYEES": "Choose employee/s", "CHOOSE_TEAMS": "Choose team/s", "CHOOSE_APPROVAL_POLICY": "<PERSON>ose Approval Policy", "EMPLOYMENT_TYPES": "Employment Type", "REGISTRATION_DATE": "Organization Registration Date", "ORGANIZATIONS": "Choose Organizations", "ORGANIZATION": "Select Organization", "DOCUMENT_NAME": "Document name", "FEEDBACK_DESCRIPTION": "Feedback description", "PREFERRED_LANGUAGE": "Preferred Language", "OWNER": "Owner", "TASK_VIEW_MODE": "Task view mode", "SELECT_STATUS": "Select Status", "LINKEDIN": "LinkedIn", "FACEBOOK": "Facebook", "INSTAGRAM": "Instagram", "TWITTER": "Twitter", "GITHUB": "<PERSON><PERSON><PERSON>", "GITLAB": "Gitlab", "UPWORK": "Upwork", "STACK_OVERFLOW": "Stackoverflow", "STATUS": "Статус", "INVOICE_NUMBER": "Invoice Number", "PROJECT_URL": "Project URL", "CLIENTS": "Клиенти", "BUDGET_TYPE": "Тип", "BUDGET": "Budget", "HOURS": "Hours", "COST": "Cost", "ADD_EDUCATION": {"SCHOOL_NAME": "School name", "DEGREE": "Degree/Diploma", "FIELD_OF_STUDY": "Field(s) of study", "DATE_OF_COMPLETION": "Date of completion", "ADDITIONAL_NOTES": "Additional notes (optional)"}, "ADD_EXPERIENCE": {"OCCUPATION": "Occupation", "ORGANIZATION": "Organization name", "DURATION": "Duration", "DESCRIPTION": "Description (optional)"}, "ADD_INTERVIEW": {"TITLE": "Title", "DATE": "Дата", "TIME": "Time", "DURATION": "Duration", "INTERVIEWERS": "Interviewers", "LOCATION": "Локация", "NOTE": "Note (optional)", "TYPE": "Interview type", "CALL": "Call", "MEETING": "Meeting"}, "MEETING_AGENDA": "Agenda", "MEETING_LOCATION": "Локация", "MEETING_DESCRIPTION": "Description", "BUFFER_TIME": "Buffer minutes", "BREAK_TIME": "Break minutes", "DESCRIPTION": "Description", "DURATION": "Duration", "TITLE": "Title", "SHORT_DESCRIPTION": "Short Description", "EG_FULL_STACK_WEB_DEVELOPER": "E.g. Full-Stack Web Developer", "COORDINATE": {"LATITUDE": "Latitude", "LONGITUDE": "Longitude"}, "SELECT_EXPENSE": "Select Expense", "ADD_TITLE": "Add title", "ALL_PROJECTS": "All Projects", "ALL_TEAMS": "All Teams", "UPWORK_API_KEY": "Upwork API key", "UPWORK_SECRET": "Upwork Secret", "SELECT_COMPANY": "Select Company", "TYPE_SEARCH_REQUEST": "Type your search request here...", "SELECT_ICON": "Select Icon", "SELECT": "Select", "SPRINT_LENGTH": "Sprint length", "SPRINT_START_DATE": "Sprint start date", "SPRINT_END_DATE": "Sprint end date", "SPRINT_GOAL": "Sprint goal", "POLICY_NAME": "Policy Name", "SELECT_DATE": "Select Date", "DAYS_UNTIL_DUE": "Days Until Due", "TEMPLATES": "Templates", "INVOICE_TEMPLATE": "Invoice Template", "ESTIMATE_TEMPLATE": "Estimate Template", "RECEIPT_TEMPLATE": "Receipt Template", "INVITATION_EXPIRATION": "Invitation Expiration", "ADD_PROJECT": "Add project", "ADD_TEAM": "Add team", "ADD_EMPLOYEE": "Add employee", "ADD_ORGANIZATION": "Add organization", "DRAG_DROP_FILE": "Drag and Drop the file here", "UPWORK_ORGANIZATION_ID": "Upwork Organization ID", "UPWORK_ORGANIZATION_NAME": "Upwork Organization Name", "UPWORK_ID": "Upwork ID", "LINKEDIN_ID": "LinkedIn ID"}, "RATES": {"DEFAULT_RATE": "Норма по подразбиране", "EXPECTED_RATE": "Expected Rate", "LIMITS": "Limits"}, "CHECKBOXES": {"INCLUDE_DELETED": "Показване на изтритите", "INCLUDE_ARCHIVED": "Include Archived", "ONLY_PAST": "Only Past", "ONLY_FUTURE": "Only Future"}, "NOTIFICATIONS": {"STARTED_WORK_ON": "It's required to enter the date when the employee started work to generate employee payroll, enable the employee to participate in split expenses, see employee statistics"}, "ARCHIVE_CONFIRMATION": {"SURE": "Are you sure you want to archive", "RECORD": "запис", "CANDIDATE": "candidate"}, "CANDIDATE_ACTION_CONFIRMATION": {"SURE": "Are you sure you want to ", "RECORD": "запис", "CANDIDATE": "candidate", "HIRE": "hire", "REJECT": "reject"}, "DELETE_CONFIRMATION": {"REMOVE_ALL_DATA": "Are you sure you want to remove all data?", "DELETE_ACCOUNT": "Are you sure you want to delete your account?", "REMOVE_USER": " from your organization", "SURE": "Потвърждавате ли изтриване на", "RECORD": "запис", "USER_RECORD": "from your organization", "EMPLOYEE": "служители", "CANDIDATE": "candidate", "EXPENSE": "Разходи", "USER": "user", "INVITATION": "покана", "DELETE_USER": " from database as it is associated only to current organization", "EVENT_TYPE": "Event type"}, "COUNTDOWN_CONFIRMATION": {"WAS": "was", "ENABLED": "enabled", "DISABLED": "disabled", "WAIT_UNTIL_RELOAD": "Please wait till application reloads"}, "ERROR": {"PROJECT_NAME": "Името на проекта е задължително.", "PROJECT_URL": "URL адресът на проекта е невалиден.", "OPEN_SOURCE_PROJECT_URL": "URL адресът на проекта с отворен код е невалиден."}}, "POP_UPS": {"SELECT_ORGANIZATION": "Моля, изберете организация от менюто по-горе.", "ADD_INCOME": "Добаветe доход", "ADD_EXPENSE": "Добавете разход", "EMPLOYEE": "Служител", "EDIT_INCOME": "Редактиране доход", "EDIT_EXPENSE": "Редактиране разход", "EDIT_PAGE": "Edit Page", "SHORT_DESCRIPTION": "Short Description", "OVERVIEW": "Overview", "COMPANY_NAME": "Company Name", "NAME": "Име", "YEAR": "Добави", "BANNER": "Banner", "SIZE": "Size", "YEAR_FOUNDED": "Year Founded", "COMPANY_SIZE": "Company Size", "CLIENT_FOCUS": "Client Focus", "DUPLICATE": "<PERSON>у<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DATE": "Дата", "PICK_DATE": "Изберете дата", "ALL_CONTACTS": "All Contacts", "CONTACT": "Contact", "ALL_VENDORS": "Търговец", "ALL_CATEGORIES": "Всички категории", "CATEGORY_NAME": "Име на категория", "EXPENSE_VALUE": "Стойност на разход", "TAX_LABEL": "Данъчен надпис", "TAX_TYPE": "Тип такса (% или Стойност)", "TAX_RATE": "Данъ<PERSON>на Ставка", "RECURRING_EXPENSES": "Повтарящи се месечни разходи", "PURPOSE": "Предназначение", "BACK_TO_WORK": "връщане на работа", "END_WORK": "Прекратяване на работа за", "START_WORK_FOR": "Start work for", "AMOUNT": "Сума", "NOTES": "Бележки", "EDIT": "Редактиране", "ADD": "Добави", "DELETE_RECURRING_EXPENSE": "Изтриване на повтарящи се разходи", "DELETE_ONLY_THIS": "Изтрии само това", "DELETE_THIS_FUTURE": "Изтрии този и бъдещи", "DELETE_ALL_ENTRIES": "Изтрии всички вписвания", "CONFIRM": "Потвърждение", "ARE_YOU_SURE_YOU_WANT_TO_RESEND_THE_INVITE_TO": "Сигурни ли сте, че искате да изпратите отново поканата", "OK": "Добре", "CANCEL": "Отказ", "ARE_YOU_SURE_YOU_WANT_TO_CHANGE_THE": "Сигурен ли си, че искаш да промениш", "RECURRING_EXPENSE": "Това е повтарящ се разход", "SPLIT_EXPENSE_WITH_INFO": "This is a split expense with original value: {{ originalValue }}, divided by the number of employees: {{ employeeCount }}", "STARTS_ON": "Starts On", "EXPENSE_HISTORY": "Expense History", "NEW_EXPENSE_VALUE": "New Expense Value", "OFFICIAL_NAME": "This name would be used in tax invoices etc.", "ADD_EVENT_TYPE": "Add Event Type", "EDIT_EVENT_TYPE": "Edit Event Type", "AWARDS": "Awards", "LEVEL": "Level", "LANGUAGES": "Languages", "TOTAL_INCOME_OR_MONTHLY_INCOME": "Total Income/Monthly Income", "PROFITS": "Profits", "BONUSES_PAID": "Bonuses Paid", "TOTAL_HOURS_WORKED_OVER_GAUZY": "Total Hours worked over Gauzy", "MINIMUM_PROJECT_SIZE": "Minimum Project Size", "PROJECTS_COUNT": "Projects Count", "CLIENTS_COUNT": "Clients Count", "EMPLOYEES_COUNT": "Employees Count", "DETAILS": "Детайли", "SKILLS": "Skills", "PRIVACY": "Privacy", "SELECT_TIMEZONE": "Select Timezone", "SHOW_AVERAGE_BONUS": "Show Average Bonus", "SHOW_AVERAGE_INCOME": "Show Average Income", "SHOW_PAYPERIOD": "Show Pay Period", "SHOW_ANONYMOUS_BONUS": "Show Anonymous Bonus", "SHOW_AVERAGE_EXPENSES": "Show Average Expenses", "SHOW_BILLRATE": "Show Bill Rate", "SHOW_START_WORK_ON": "Show Started Work On", "SHOW_CLIENTS": "Show Clients", "DISPLAY_BONUS_ANONYMOUSLY": "Display bonus Anonymously", "SOURCE": "Source", "DESCRIPTION": "Description", "MAIN": "Основни", "CATEGORIES": "Categories", "ADD_LANGUAGE": "Add language", "REGISTER_AS_EMPLOYEE_TOOLTIP": "You must be employee in order to be able to track time, create split expenses and use other employees related features."}, "MENU": {"PIPELINES": "Pipelines", "DASHBOARD": "Табло", "DASHBOARDS": "Dashboards", "APPOINTMENTS": "Appointments", "ACCOUNTING": "Accounting", "INCOME": "Доход", "EXPENSES": "Разходи", "RECURRING_EXPENSE": "Recurring Expenses", "POSITIONS": "Позиции", "INTEGRATIONS": "Apps & Integrations", "UPWORK": "Upwork", "PROPOSALS": "Предложения", "TIME_OFF": "Почивка", "APPROVALS": "Approvals", "HELP": "Помощ", "ABOUT": "За нас", "CONTACTS": "Contacts", "ADMIN": "Администратор", "EMPLOYEE_LEVEL": "Employee Level", "EMPLOYEES": "Служители", "MANAGE": "Управление", "CANDIDATES": "Candidates", "ORGANIZATIONS": "Организации", "SETTINGS": "Настройки", "GENERAL": "Основни", "EMAIL_HISTORY": "Email History", "USERS": "Потребители", "ROLES": "Роли и Позволения", "DANGER_ZONE": "Опасна Зона", "FILE_STORAGE": "File Storage", "INVITE_PEOPLE": "Invite People", "IMPORT_EXPORT": {"IMPORT_EXPORT": "Import/Export", "IMPORT_EXPORT_DATA": "Import / Export Data", "IMPORT": "Import", "IMPORT_HISTORY": "Import History", "IMPORT_DATE_TIME": "Imported Date & Time", "EXPORT": "Export", "MERGE": "Merge data", "CLEAN_UP": "Clean up before import", "EXPORT_MESSAGE": "Click \"EXPORT\" to download all tables from the database. ", "IMPORT_MESSAGE": "Click \"IMPORT\" to insert all tables in the database. The name of the file for upload should be: \"archive.zip\". Choose also between options \"Merge data\" and \"Clean up before import\".", "IMPORT_HISTORY_MESSAGE": "You can track your recently imported files here", "SELECT_FILE": "Select File", "DROP_FILE": "Drop the file here", "NO_DROP_FILE": "Can't drop the file here", "BROWSE": "BROWSE", "NAME": "Име", "SIZE": "Size", "PROGRESS": "Progress", "STATUS": "Статус", "ACTIONS": "Actions", "QUEUE_PROGRESS": "Queue progress:", "CANCEL": "Отказ", "REMOVE": "Remove", "WRONG_FILE_NAME": "Wrong file name!!!", "CORRECT_FILE_NAME": "The file name should be: \"archive.zip\"", "DOWNLOAD_TEMPLATES": "Download templates", "MIGRATE_TO_CLOUD": "Migrate to <PERSON>", "IMPORT_INFO": "Click \"Import\" to import your data into Gauzy DB.", "EXPORT_INFO": "Click \"Export\" to export data from Gauzy DB.", "DOWNLOAD_TEMPLATES_INFO": "Click \"Download Templates\" to download Zip archive with CSV templates for the format of Gauzy data.", "MIGRATE_TO_CLOUD_INFO": "Click \"Migrate to Ever Gauzy Cloud\" to migrate from self-hosted to cloud edition.", "MIGRATE_SUCCESSFULLY": "Gauzy Cloud migration for '{{ tenant }}' successfully!", "ACCOUNTING_TEMPLATE": "Accounting Template", "ACTIVITY": "Activity", "APPROVAL_POLICY": "Approval Policy", "AVAILABILITY_SLOT": "Availability Slot", "CANDIDATE": "Candidate", "CONTACT": "Contact", "COUNTRY": "Държава", "CURRENCY": "Валута", "DEAL": "Deal", "EMAIL": "Eлектронна поща", "EMPLOYEE": "Служител", "EQUIPMENT": "Equipment", "EVENT_TYPES": "Event Types", "EXPENSE": "Разходи", "GOAL": "Goal", "INCOME": "Доход", "INTEGRATION": "Integration", "INVITE": "Покана", "INVOICE": "Фактура", "JOB": "Job", "KEY_RESULT": "Key Results", "KNOWLEDGE_BASE": "Knowledge Base", "LANGUAGE": "<PERSON>зи<PERSON> (Language)", "MERCHANT": "Merchant", "ORGANIZATION": "Organization", "PAYMENT": "Payment", "PIPELINE": "Pipeline", "PIPELINE_STAGE": "Pipeline Stage", "PRODUCT": "Product", "PROPOSAL": "Предложение", "REPORT": "Report", "REQUEST_APPROVAL": "Request Approval", "ROLE": "Роля", "SKILL": "Skill", "TAG": "Tag", "TASK": "Задача", "TENANT": "Tenant", "TENANT_SETTING": "Tenant Setting", "TIME_OFF_POLICY": "Time off policy", "TIME_SHEET": "Time Sheet", "USER": "User", "CANDIDATE_CRITERION_RATING": "Candidate Criterion Rating", "CANDIDATE_DOCUMENT": "Candidate Document", "CANDIDATE_EDUCATION": "Candidate Education", "CANDIDATE_EXPERIENCE": "Candidate Experience", "CANDIDATE_FEEDBACK": "Candidate <PERSON><PERSON><PERSON>", "CANDIDATE_INTERVIEW": "Candidate Interview", "CANDIDATE_INTERVIEWER": "Candidate Interviewer", "CANDIDATE_PERSONAL_QUALITY": "Candidate Personal Quality", "CANDIDATE_SKILL": "Candidate <PERSON><PERSON>", "CANDIDATE_SOURCE": "Candidate Source", "CANDIDATE_TECHNOLOGY": "Candidate Technology", "ORGANIZATION_AWARD": "Organization Award", "ORGANIZATION_CONTACT": "Organization Contact", "ORGANIZATION_DEPARTMENT": "Organization Department", "ORGANIZATION_DOCUMENT": "Organization Document", "ORGANIZATION_EMPLOYEE_LEVEL": "Organization Employee Level", "ORGANIZATION_EMPLOYMENT_TYPE": "Organization Employment Type", "ORGANIZATION_LANGUAGES": "Organization Languages", "ORGANIZATION_POSITION": "Organization Position", "ORGANIZATION_PROJECT": "Organization Project", "ORGANIZATION_RECURRING_EXPENSE": "Задължителни разходи в организацията", "ORGANIZATION_SPRINT": "Organization Sprint", "ORGANIZATION_TEAM": "Organization Team", "ORGANIZATION_TEAM_EMPLOYEE": "Organization Team Employee", "ORGANIZATION_VENDOR": "Organization Vendor", "EMAIL_TEMPLATE": "<PERSON>ail Te<PERSON>late", "ESTIMATE_EMAIL": "Estimate Email", "EMPLOYEE_APPOINTMENT": "Employee Appointment", "EMPLOYEE_AWARD": "Employee Award", "EMPLOYEE_PROPOSAL_TEMPLATE": "Employee Proposal Template", "EMPLOYEE_RECURRING_EXPENSE": "Разходи, повтарящи се за служителя", "EMPLOYEE_SETTING": "Employee Setting", "EMPLOYEE_UPWORK_JOB_SEARCH_CRITERION": "Employee Upwork Job Search Criterion", "INTEGRATION_ENTITY_SETTING": "Integration Entity Setting", "INTEGRATION_ENTITY_SETTING_TIED_ENTITY": "Integration Entity Setting Tied Entity", "INTEGRATION_MAP": "Integration Map", "INTEGRATION_SETTING": "Integration Setting", "INTEGRATION_TENANT": "Integration Tenant", "INTEGRATION_TYPE": "Integration Type", "INVITE_ORGANIZATION_CONTACT": "Invite Organization Contact", "INVITE_ORGANIZATION_DEPARTMENT": "Invite Organization Department", "INVITE_ORGANIZATION_PROJECT": "Invite Organization Project", "PRODUCT_CATEGORY": "Product Category", "PRODUCT_CATEGORY_TRANSLATION": "Product Category Translation", "PRODUCT_GALLERY_ITEM": "Product Gallery Item", "PRODUCT_OPTION": "Product Option", "PRODUCT_OPTION_GROUP": "Product Option Group", "PRODUCT_OPTION_GROUP_TRANSLATION": "Product Option Group Translation", "PRODUCT_OPTION_TRANSLATION": "Product Option Translation", "PRODUCT_STORE": "Product Store", "PRODUCT_TRANSLATION": "Product Translation", "PRODUCT_TYPE": "Product Type", "PRODUCT_TYPE_TRANSLATION": "Product Type Translation", "PRODUCT_VARIANT": "Product Variant", "PRODUCT_VARIANT_PRICE": "Product Variant Price", "PRODUCT_VARIANT_SETTING": "Product Variant Setting", "REPORT_CATEGORY": "Report Category", "REPORT_ORGANIZATION": "Report Organization", "REQUEST_APPROVAL_TAG": "Request Approval Tag", "REQUEST_APPROVAL_EMPLOYEE": "Request Approval Employee", "REQUEST_APPROVAL_TEAM": "Request Approval Team", "SKILL_EMPLOYEE": "<PERSON><PERSON> Employee", "SKILL_ORGANIZATION": "Skill Organization", "TAG_CANDIDATE": "Tag Candidate", "TAG_EMPLOYEE": "Tag Employee", "TAG_EQUIPMENT": "Tag Equipment", "TAG_EVENT_TYPE": "Tag Event Type", "TAG_EXPENSE": "Tag Expense", "TAG_INCOME": "Tag Income", "TAG_INVOICE": "Tag Invoice", "TAG_ORGANIZATION_CONTACT": "Tag Organization Contact", "TAG_ORGANIZATION_DEPARTMENT": "Tag Organization Department", "TAG_ORGANIZATION_EMPLOYEE_LEVEL": "Tag Organization Employee Level", "TAG_ORGANIZATION_EMPLOYEE_TYPE": "Tag Organization Employee Type", "TAG_ORGANIZATION_EXPENSES_CATEGORY": "Tag Organization Expense Category", "TAG_ORGANIZATION_POSITION": "Tag Organization Position", "TAG_ORGANIZATION_PROJECT": "Tag Organization Project", "TAG_ORGANIZATION_TEAM": "Tag Organization Team", "TAG_ORGANIZATION_VENDOR": "Tag Organization Vendor", "TAG_ORGANIZATIONS": "Tag Organizations", "TAG_PAYMENT": "Tag Payments", "TAG_PRODUCT": "Tag Product", "TAG_PROPOSAL": "Tag Proposal", "TAG_TASK": "Tag Task", "TAG_USER": "Tag User", "TASK_EMPLOYEE": "Tag Employee", "TASK_TEAM": "Tag Team", "EQUIPMENT_SHARING": "Equipment Sharing", "EQUIPMENT_SHARE_POLICY": "Equipment Share Policy", "EXPENSE_CATEGORY": "Expense Category", "GOAL_KPI": "Goal Kpi", "GOAL_GENERAL_SETTING": "Goal General Setting", "GOAL_KPI_TEMPLATE": "Goal Kpi Template", "GOAL_TEMPLATE": "Goal Template", "GOAL_TIME_FRAME": "Goal Time Frame", "KNOWLEDGE_BASE_ARTICLE": "Knowledge Base Article", "KNOWLEDGE_BASE_AUTHOR": "Knowledge Base Author", "INVOICE_ESTIMATE_HISTORY": "Invoice Estimate History", "INVOICE_ITEM": "Invoice Item", "JOB_PRESET": "Job Preset", "JOB_PRESET_UPWORK_SEARCH_CRITERION": "Job Preset Upwork Search Criterion", "JOB_SEARCH_OCCUPATION": "Job Search Occupation", "JOB_SEARCH_CATEGORY": "Job Search Category", "KEY_RESULT_TEMPLATE": "Key Result Template", "KEY_RESULT_UPDATE": "Key Result Update", "ROLE_PERMISSION": "Role Permission", "TIME_OFF_POLICY_EMPLOYEE": "Time Off Policy Employee", "TIME_OFF_REQUEST": "Time Off Request", "TIME_OFF_REQUEST_EMPLOYEE": "Time Off Request Employee", "SCREENSHOT": "Screenshot", "TIME_LOG": "Time Log", "TIME_SLOT": "Time Slot", "TIME_SLOT_MINUTES": "Time Slot Minutes", "TIME_SLOT_TIME_LOGOS": "Time Slot Time Logs", "USER_ORGANIZATION": "User Organization", "WAREHOUSE": "Warehouse", "WAREHOUSE_MERCHANT": "Warehouse Merchant", "WAREHOUSE_PRODUCT": "Warehouse Product", "WAREHOUSE_PRODUCT_VARIANT": "Warehouse Product Variant", "WAREHOUSE_STORE": "Warehouse Store", "PRODUCT_IMAGE_ASSET": "Product Image Asset", "CUSTOM_SMTP": "Custom SMTP", "FEATURE": "Feature", "FEATURE_ORGANIZATION": "Feature Organization", "EXPORT_DATA": "Export Data", "IMPORT_DATA": "Import Data", "ALL_ENTITIES": "ALL Entities"}, "TAGS": "Тагове", "LANGUAGE": "<PERSON>зи<PERSON> (Language)", "LANGUAGES": "Languages", "EQUIPMENT": "Equipment", "EQUIPMENT_SHARING": "Equipment Sharing", "TASKS": "Tasks", "TASKS_SETTINGS": "Настройки", "INVOICES": "Invoices", "ORGANIZATION": "Organization", "TENANT": "Tenant", "RECURRING_INVOICES": "Invoices Recurring", "INVOICES_RECEIVED": "Invoices Received", "ESTIMATES_RECEIVED": "Estimates Received", "ESTIMATES": "Estimates", "MY_TASKS": "My Tasks", "JOBS": "Jobs", "PROPOSAL_TEMPLATE": "Proposal Template", "JOBS_SEARCH": "Browse", "JOBS_MATCHING": "Matching", "TEAM_TASKS": "Team's Tasks", "TIME_ACTIVITY": "Time & Activity", "TIMESHEETS": "Timesheets", "SCHEDULES": "Schedules", "EMAIL_TEMPLATES": "Email Templates", "REPORTS": "Reports", "GOALS": "Goals", "ALL_REPORTS": "All Reports", "TIME_REPORTS": "Time Report", "WEEKLY_TIME_REPORTS": "Weekly Report", "ACCOUNTING_REPORTS": "Accounting Reports", "PAYMENT_GATEWAYS": "Payment Gateways", "SMS_GATEWAYS": "SMS Gateways", "CUSTOM_SMTP": "Custom SMTP", "INVENTORY": "Inventory", "SALES": "Sales", "PAYMENTS": "Payments", "FEATURES": "Features", "ACCOUNTING_TEMPLATES": "Accounting Templates", "FOCUS": "Focus", "APPLICATIONS": "Applications", "OPEN_GA_BROWSER": "Отвори Gauzy в браузъра", "START_SERVER": "Стар<PERSON><PERSON><PERSON><PERSON><PERSON> сървъра", "STOP_SERVER": "Спри сървъра"}, "SETTINGS_MENU": {"THEMES": "Теми", "LIGHT": "Светла", "DARK": "Тъмна", "COSMIC": "Космическа", "CORPORATE": "Корпоративна", "MATERIAL_LIGHT_THEME": "Material Light", "MATERIAL_DARK_THEME": "Material Dark", "GAUZY_LIGHT": "Gauzy Light", "GAUZY_DARK": "Gauzy <PERSON>", "LANGUAGE": "<PERSON>зи<PERSON> (Language)", "ENGLISH": "Английски", "FRENCH": "Френски", "SPANISH": "Испански", "BULGARIAN": "Български", "HEBREW": "<PERSON>в<PERSON><PERSON>т", "RUSSIAN": "Руски", "CHINESE": "китайски", "GERMAN": "Немски", "PORTUGUESE": "Португалски", "ITALIAN": "Италиански", "DUTCH": "Нидерландски", "POLISH": "Полски", "ARABIC": "Арабски", "PREFERRED_LAYOUT": "Layout", "PREFERRED_LAYOUT_TOOLTIP": "This will only set the default layout and not change the layout on each page if you have already changed it once.", "RESET_LAYOUT": "Reset Layout", "RESET_LAYOUT_TOOLTIP": "Reset layout to default for all pages", "TABLE": "Table", "CARDS_GRID": "Cards Grid", "SPRINT_VIEW": "Sprint View", "QUICK_SETTINGS": "Quick Settings"}, "CHANGELOG_MENU": {"HEADER": "What's new?", "LEARN_MORE_URL": "Learn more", "GAUZY_FEATURES": "Gauzy features"}, "REPORT_PAGE": {"MEMBERS_WORKED": "Members Worked", "MANUAL_TIME_EDIT_REPORT": "Manual Time Edit Report", "GROUP_BY": "Group", "DATE": "Дата", "TO_DO": "To Do", "TIME": "Time", "PROJECT": "Проект", "CLIENT": "Кли<PERSON><PERSON>т", "NOTES": "Бележки", "PROJECTS_WORKED": "Projects Worked", "APPS_AND_URLS_REPORT": "Apps & URLs Report", "ACTIVITY": "Activity", "TOTAL_HOURS": "Total Hours", "EMPLOYEE": "Служител", "EMPLOYEES/TEAMS": "Employees /Teams", "NO_PROJECT": "No Project", "NO_EMPLOYEE": "No Employee", "TITLE": "Title", "ACTION": "Action", "TIME_SPAN": "Time Span", "REASON": "Reason", "CHANGED_AT": "Changed at", "DURATION": "Duration", "NO_TASK": "No Task", "FROM": "From", "WEEKLY_TIME_AND_ACTIVITY_REPORT": "Weekly Time and Activity Report", "TIME_AND_ACTIVITY_REPORT": "Time and Activity Report", "NO_CLIENT": "No Client", "ALL_REPORTS": "All Reports", "EXPENSES_REPORT": "Expenses Report", "CATEGORY": "Категория", "DESCRIPTION": "Description", "AMOUNT": "Сума", "NO_EXPENSES": "No Expenses", "PAYMENT_REPORT": "Payments Report", "NO_PAYMENTS": "No Payments", "CONTACT": "Contact", "CURRENCY": "Валута", "NOTE": "Note", "AMOUNT_OWED": "Amount Owed Report", "CURRENT_RATE": "Current Rate", "HOURS": "Hours", "SPENT": "Spent", "BUDGET": "Budget", "REMAINING": "Remaining", "WEEKLY_LIMIT_REPORT": "Weekly Limit Report", "NO_EMPLOYEES": "No Employees", "DAILY_LIMIT_REPORT": "Daily Limit Report", "LIMIT": "Limit", "SPENT_HOURS": "Spent Hours", "REMAINING_HOURS": "Remaining Hours", "PROJECT_BUDGET_REPORTS": "Project Budget Reports", "CLIENT_BUDGET_REPORTS": "Client Budget Reports", "EXPENSE": "Разходи", "PAYMENT": "Payment", "NO_EMPLOYEES_WORKED": "No employees worked", "WEEKLY_TOTAL": "Weekly Total", "NO_DATA": {"APP_AND_URL_ACTIVITY": "No records found. Please select date range, employee or project.", "MANUAL_ACTIVITY": "No records found. Please select date range, employee or project.", "AMOUNT_OWED": "No Amount Owed", "WEEKLY_TIME_AND_ACTIVITY": "You have not any tracked time and activity yet for these day's.", "DAILY_TIME_AND_ACTIVITY": "There is no time and activity tracked yet for this day. Please use the <a href={{downloadURL}} rel=\"noopener\" target=\"_blank\">Gauzy Desktop Timer App</a>.", "PROJECT_BUDGET": "You have not created any project with budget.", "CLIENT_BUDGET": "You have not created any client with budget."}}, "INTEGRATIONS": {"AVAILABLE_INTEGRATIONS": "Available Apps & Integrations", "ADDED_UPWORK_TRANSACTION": "Added Upwork Transaction", "TOTAL_UPWORK_TRANSACTIONS_SUCCEED": "Total expense transactions succeed: {{ totalExpenses }}. Total income transactions succeed: {{ totalIncomes }}", "HUBSTAFF_PAGE": {"NAME": "<PERSON><PERSON><PERSON><PERSON>", "SELECT_ORGANIZATION": "Select Organization", "SYNCED_PROJECTS": "Projects synced", "SETTINGS_UPDATED": "Updated settings for integration", "SYNCED_ENTITIES": "Auto synced entities", "TOOLTIP_ACTIVITY_INFO": "Date range Limit: 7 days Earliest Date: 6 full months", "DATE_RANGE_PLACEHOLDER": "Choose date between", "CLIENT_ID": "Hubstaff Client ID", "CLIENT_SECRET": "Hubstaff Client Secret", "GRANT_PERMISSION": "Next you will be taken to Hubstaff to grant permission to <PERSON><PERSON><PERSON>.", "ENTER_CLIENT_SECRET": "Enter client secret to get access token."}, "UPWORK_PAGE": {"ACTIVITIES": "Activities", "REPORTS": "Reports", "TRANSACTIONS": "Transactions", "SUCCESSFULLY_AUTHORIZED": "Successfully authorized", "API_KEY": "Upwork API key", "SECRET": "Upwork Secret", "NEXT_STEP_INFO": "Next you will be taken to Upwork to grant permission to <PERSON><PERSON><PERSON>.", "CONTRACTS": "Contracts", "SYNCED_CONTRACTS": "Synced Contracts", "SELECT_DATE": "Select Date", "ONLY_CONTRACTS": "Only Contracts", "CONTRACTS_RELATED_DATA": "Synced Contracts related entities", "DATE_RANGE_PLACEHOLDER": "Choose date between", "HOURLY": "Hourly"}, "COMING_SOON": "Coming soon", "RE_INTEGRATE": "Re-integrate", "SETTINGS": "Настройки", "SELECT_GROUPS": "Select Groups", "FILTER_INTEGRATIONS": "Filter integrations", "SEARCH_INTEGRATIONS": "Search integrations", "PAID": "Paid", "INTEGRATION": "Integration"}, "DASHBOARD_PAGE": {"ACCOUNTING": "Счетоводство", "HUMAN_RESOURCES": "Човешки ресурси", "TIME_TRACKING": "Проследяване на времето", "PROJECT_MANAGEMENT": "Управление на проекти", "EMPLOYEE_STATISTICS": "Статистика на служителите", "SELECT_A_MONTH_AND_EMPLOYEE": "Моля, изберете един месец и служител от менюто по-горе", "INSERT_TEXT_FOR_NOT_AUTHENTICATED_USERS": "Добави текст за неаутентикирани потребители", "CHARTS": {"BAR": "Bar", "DOUGHNUT": "Doughnut", "STACKED_BAR": "Stacked Bar", "CHART_TYPE": "Chart Type", "REVENUE": "Revenue", "EXPENSES": "Разходи", "PROFIT": "Печалба", "BONUS": "<PERSON>о<PERSON><PERSON><PERSON>", "NO_MONTH_DATA": "No Data for this month", "CASH_FLOW": "Cash Flow", "WORKING": "Working", "WORKING_NOW": "Working now", "NOT_WORKING": "Not working", "WORKING_TODAY": "Working today"}, "PROFIT_HISTORY": {"PROFIT_REPORT": "Репорт на печалбата", "TOTAL_EXPENSES": "Общи разходи", "TOTAL_INCOME": "Общ доход", "TOTAL_PROFIT": "Печалба Общо", "DATE": "Дата", "EXPENSES": "Разходи", "INCOME": "Доход", "DESCRIPTION": "Description"}, "TITLE": {"PROFIT_REPORT": "Репорт на Печалбата", "TOTAL_EXPENSES": "Общи разходи", "TOTAL_INCOME": "Общ доход", "PROFIT": "Печалба", "TOTAL_BONUS": "Бонус Общо", "TOTAL_DIRECT_INCOME": "Изтриване на доход", "SALARY": "Заплата", "TOTAL_DIRECT_INCOME_INFO": "Income from direct bonus", "TOTAL_INCOME_CALC": "Total Income = Income {{ totalNonBonusIncome }} + Direct Income {{ totalBonusIncome }}", "TOTAL_PROFIT_BONUS": "Total Profit Bonus", "TOTAL_DIRECT_BONUS": "Direct Income Bonus", "TOTAL_DIRECT_BONUS_INFO": "This is equal to the direct income", "TOTAL_PROFIT_BONUS_INFO": "{{ bonusPercentage }}% of the profit {{ difference }}", "TOTAL_INCOME_BONUS": "Total Income Bonus", "TOTAL_INCOME_BONUS_INFO": "{{ bonusPercentage }}% от доходи {{ totalIncome }}", "TOTAL_EXPENSE_CALC": "Total = Employee Expenses + Split Expenses + Recurring + Salary", "TOTAL_EXPENSES_WITHOUT_SALARY": "Total Expense without salary", "TOTAL_EXPENSES_WITHOUT_SALARY_CALC": "Expense = Employee Expenses + Split Expenses + Recurring", "TOTAL_BONUS_CALC": "Total Bonus = Direct Income Bonus {{ totalBonusIncome }} + Bonus {{ calculatedBonus }}"}, "DEVELOPER": {"DEVELOPER": "Разработчик", "AVERAGE_BONUS": "Средно Месечен Бонус", "TOTAL_INCOME": "Общ доход", "TOTAL_EXPENSES": "Общи разходи", "PROFIT": "Печалба", "PROFIT_CALC": "Profit (Net Income) = Total Income {{ totalAllIncome }} - Total Expenses {{ totalExpense }}", "NOTE": "Забележка: отрицателните бонуси трябва да се приспадат през следващите месеци от положителните бонуси преди окончателните плащания на бонуси", "BONUS": "<PERSON>о<PERSON><PERSON><PERSON>", "EMPLOYEES": "Служители"}, "ADD_INCOME": "Добавете ново въвеждане на доходи", "ADD_EXPENSE": "Добавете ново въвеждане на разходи", "RECURRING_EXPENSES": "Recurring Expenses", "ADD_ORGANIZATION_RECURRING_EXPENSE": "Add New Recurring Expense Entry for Organization", "ADD_EMPLOYEE_RECURRING_EXPENSE": "Add New Recurring Expense Entry for Employee", "PLAN_MY_DAY": "Plan My Day", "ADD_TODO": "Add <PERSON>", "MOST_VIEW_PROJECTS": "Most Viewed Projects", "INBOX": "Inbox", "RECENTLY_ASSIGNED": "Recently Assigned", "NO_TODO_ASSIGNED": "No Todo Assigned"}, "INCOME_PAGE": {"INCOME": "Доход", "BONUS_HELP": "Ако е зададено, процента такса от компанията няма да бъде добавен към бонуса", "BONUS_TOOLTIP": "Това е директен бонус", "EMPLOYEES_GENERATE_INCOME": "Employees that generate income"}, "EXPENSES_PAGE": {"EXPENSES": "Разходи", "MUTATION": {"CONTACT_IS_REQUIRED": "Contact is required!", "PLEASE_SELECT_A_CONTACT_OR_CHANGE_EXPENSE_TYPE": "Please select a Contact from the dropdown menu below or change Expense type setting.", "ASSIGN_TO": "Възложи на", "INCLUDE_TAXES": "Включете такси", "ATTACH_A_RECEIPT": "Приложете разписка", "EMPLOYEES_GENERATE_EXPENSE": "Employees that generate expense", "TAX_DEDUCTIBLE": "Tax Deductible", "NOT_TAX_DEDUCTIBLE": "Not Tax Deductible", "BILLABLE_TO_CONTACT": "Billable to Contact", "PERCENTAGE": "Percentage", "VALUE": "Стойност", "TAX_AMOUNT": "Tax Amount", "TAX_RATE": "Tax Rate", "INVOICED": "Invoiced", "UNINVOICED": "Uninvoiced", "PAID": "Paid", "NOT_BILLABLE": "Not Billable"}, "DEFAULT_CATEGORY": {"SALARY": "Заплата", "SALARY_TAXES": "Salary Taxes", "RENT": "Rent", "EXTRA_BONUS": "Допълнителен бонус"}, "SPLIT_HELP": "If set, this expense will be equally divided amongst all employees of the company & will be considered in each one's expenses.", "SPLIT_WILL_BE_TOOLTIP": "This expense will be equally split amongst all employees", "SPLIT_EXPENSE": "Split Expense", "ADD_EXPENSE_CATEGORY": "Add New Expense category", "EXPENSE_CATEGORY": "Expense Category", "RECURRING_EXPENSES": {"WARNING": "Warning: This period overlaps with an existing previous record(s):", "FROM": "From", "TO": "to", "VALUE_OVERWRITTEN": "The value will be overwritten from", "ERROR": "Error: This period overlaps with existing future record(s).", "NOT_SUPPORTED": "This is not supported, please edit the future expenses instead.", "EDIT_FUTURE_VALUE": "This will only edit the future value from", "EXISTING_VALUE": "existing value of", "STARTED_ON": "Started on", "AFFECTED": "will not be affected until", "SET_EXPENSE_VALUE": "This will set the expense value", "ONWARDS": "onwards and the existing value of", "ENDING_ON": "and ending on", "SET_UNTIL": "will be now set until", "REDUCE_START_DATE": "This will reduce the start date and include all the months from", "FOR_EXPENSE_VALUE": "for expense value", "CHANGE_START_DATE": "This will change the start date to"}}, "EMPLOYEES_PAGE": {"HEADER": "Manage Employees", "ADD_EMPLOYEE": "Добавяне на служител", "ACTIVE": "Акти<PERSON><PERSON>н", "END_WORK": "Край на работа", "WORK_ENDED": "Завършена работа", "DELETED": "Изтрит", "ENABLED": "Enabled", "DISABLED": "Disabled", "RECURRING_EXPENSE": "Разходи, повтарящи се за служителя", "RECURRING_EXPENSE_EDIT": "Редактирай текущите и всички бъдещи разходи. Няма да има промяна по ...", "RECURRING_EXPENSE_ADD": "Това ще добави разход повтарящи се ежемесечно.", "RECURRING_EXPENSE_SET": "'{{ name }}' recurring expense set.", "RECURRING_EXPENSE_EDITED": "'{{ name }}' recurring expense edited.", "RECURRING_EXPENSE_DELETED": "'{{ name }}' recurring expense deleted.", "EMPLOYEE_NAME": "Служител", "BACK_TO_WORK": "Връщане на работа", "SELECT_EMPLOYEE_MSG": "Моля селектирайте служител от менюто по-долу.", "EDIT_EMPLOYEE": {"HEADER": "Редактирай служителя", "DEVELOPER": "Разработчик", "DEPARTMENT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "POSITION": "Позиция", "EMPLOYEE_DEPARTMENTS": "Отдел на Служителя:", "EMPLOYEE_PROJECTS": "Проект на Служителя:", "EMPLOYEE_CONTACTS": "Employee's Contacts:", "EMPLOYMENT_TYPE": "Employment Type", "ACCOUNT": "Account", "EMPLOYMENT": "Employment", "LOCATION": "Локация", "RATES": "Rates", "PROJECTS": "Проекти", "CONTACTS": "Contacts", "HIRING": "Hiring", "NETWORKS": "Networks", "EMPLOYEE_LEVEL": "Employee Level", "DISPLAY_BONUS_ANONYMOUSLY": "Display bonus Anonymously", "JOB_SUCCESS": "Job Success", "TOTAL_JOBS": "Total Jobs", "TOTAL_HOURS": "Total Hours", "RATE": "Почасово", "VETTED": "Vetted", "HR": "hr", "SETTINGS": "Настройки", "GENERAL_SETTINGS": "Основни настройки"}, "ADD_EMPLOYEES": {"STEP_1": "Step 1", "STEP_2": "Step 2", "STEP_3": "Step 3", "ADD_ANOTHER_EMPLOYEE": "Add Another Employee", "FINISHED_ADDING": "I’ve Added All Current Employees", "NEXT": "Next", "PREVIOUS": "Previous"}, "NOT_STARTED": "Not Started", "NOT_STARTED_HELP": "Started work on date is not set for this employee. The employee will not be considered in accounts, split expenses etc."}, "GOALS_PAGE": {"HEADER": "All Objectives", "GOAL": "Goal", "GOALS_EMPTY": "You haven't created any Objectives yet. Click Add New to create one", "ADD_NEW_KEY_RESULT": "Add new key result", "ADD_NEW_OBJECTIVE": "Add new Objective", "EDIT_OBJECTIVE": "Edit Objective", "SESSION": "Session", "GOAL_SETTINGS": "Goals Settings", "NO_DESCRIPTION": "No Description", "PROGRESS": "Progress", "EXPECTED": "Expected", "UPDATES": "Updates", "UPDATE": "Update", "COMMENTS": "Comments", "KEY_RESULTS": "Key Results", "GROUP_BY": "Group By", "DELETE_OBJECTIVE": "Delete Objective", "DELETE_KEY_RESULT": "Delete Key Result", "ARE_YOU_SURE": "Are you sure? This action is irreversible.", "ALL_OBJECTIVES": "All Objectives", "MY_TEAMS_OBJECTIVES": "My Team's Objectives", "MY_ORGANIZATIONS_OBJECTIVES": "'My Organization Objectives", "MY_OBJECTIVES": "My Objectives", "OBJECTIVE_LEVEL": "Objective Level", "TIME_FRAME": "Time Frames", "CREATE_NEW": "+ create new", "GOAL_TEMPLATES": "Goal Templates", "CREATE_NEW_MENU": "Create New", "CREATE_FROM_PRESET": "Create from Preset", "OWNERSHIP": {"EMPLOYEES": "Служители", "TEAMS": "Отбори", "EMPLOYEES_AND_TEAMS": "Employees and Teams"}, "SETTINGS": {"ADD_TIME_FRAME_TITLE": "Add Time Frame", "EDIT_TIME_FRAME_TITLE": "Edit Time Frame", "TIME_FRAME_PAGE_TITLE": "Set Time Frame", "PREDEFINED_TIME_FRAMES": "Predefined Time Frames", "ADD_KPI": "Add KPI", "EDIT_KPI": "Edit KPI", "MAX_ENTITIES": "Max Number of entities that can be created", "EMPLOYEE_OBJECTIVES": "Employees can create their own objectives", "WHO_CAN_OWN_OBJECTIVES": "Who can own Objectives?", "WHO_CAN_OWN_KEY_RESULTS": "Who can own Key Results?", "ADD_KPI_TO_KEY_RESULT": "Add KPI to Key Result Type?", "ADD_TASK_TO_KEY_RESULT": "Add Task to Key Result Type?", "GENERAL": "Основни", "KPI": "KPI", "DELETE_TIME_FRAME_TITLE": "Delete Time Frame", "DELETE_TIME_FRAME_CONFIRMATION": "Are you sure you want to delete Time Frame?", "DELETE_KPI_TITLE": "Delete KPI", "DELETE_KPI_CONFIRMATION": "Are you sure you want to delete KPI?", "ANNUAL": "Annual"}, "MESSAGE": {"NO_KEY_RESULT": "No Key Results to display.", "NO_UPDATES": "No Updates yet.", "NO_ALIGNMENT": "No Alignments yet."}, "LEVELS": {"ORGANIZATION": "Organization", "TEAM": "Team", "EMPLOYEE": "Служител"}, "TIME_FRAME_STATUS": {"ACTIVE": "Акти<PERSON><PERSON>н", "INACTIVE": "Inactive"}, "KPI_OPERATOR": {"GREATER_THAN_EQUAL_TO": "Greater than or equal to", "LESSER_THAN_EQUAL_TO": "Lesser than or equal to"}, "KPI_METRIC": {"NUMERICAL": "Numerical", "PERCENTAGE": "Percentage", "CURRENCY": "Валута"}, "TOOLTIPS": {"PROGRESS": "Overall progress of this Objective based on its Key result's progress", "DETAILS": "Objective Details", "EDIT": "Edit Objective"}, "FORM": {"LABELS": {"LEVEL": "Level", "OWNER": "Owner", "LEAD": "Lead", "LEAD_OPTIONAL": "Lead (optional)", "DEADLINE": "Deadline", "STATUS": "Статус", "KPI_SHOULD_BE": "This KPI should be", "KPI_METRIC": "KPI Metric", "CURRENT_VALUE": "Current Value", "OBJECTIVE": "Objectives", "KEY_RESULT": "Key Results"}, "PLACEHOLDERS": {"NAME": "Objective Name. eg. Improve Website SEO", "DESCRIPTION": "Objective Description.", "LEVEL": "Goal Level", "TIME_FRAME_NAME": "Time frame name eg. 'Q4-2020'", "KPI_DESCRIPTION": "A short description to give some context about the KPI", "KPI_NAME": "KPI Title. eg. Maintain page views - 200/day"}, "ERROR": {"START_DATE_GREATER": "End Date must be greater than Start Date"}}, "BUTTONS": {"ADD_TIME_FRAME": "Add Time Frame"}, "HELPER_TEXT": {"OBJECTIVE_GENERAL": "An <b>Objective</b> is a description of a goal to be achieved in the future.", "OBJECTIVE_TITLE": "<p>Create an ambitious title that best describes your goal.</p><p>The title should not contain any metric</p>eg.<ul><li>Generate more revenue than last year</li><li>Become market leader</li><li>Increase website engagement</li></ul>", "OBJECTIVE_DESCRIPTION": "Write a short description to give some context to your Objective", "OBJECTIVE_LEVEL": "The group to which this Objective belongs to.", "OBJECTIVE_OWNER": "The group/ individual responsible for completing the Objective.", "OBJECTIVE_LEAD": "The person responsible for organizing and planning that is needed to achieve the Objective.", "OBJECTIVE_TIMEFRAME": "Time Frame for which this Objective is created. If a time frame doesn't exist, create one here.", "KPI_GENERAL": "Organizations use <b> Key Performance Indicators (KPIs) </b> to evaluate their success at reaching targets. KPIs can be used as measurable metrics for Key results.", "KPI_NAME": "<p>Name your new KPI</p><p>Examples for good KPI title</p><ul><li># of Followers</li><li>Annual Recurring Revenue</li><li>Customer Lifetime Value</li></ul>", "KPI_DESCRIPTION": "Write a short description to give some context to your KPI", "KPI_METRIC": "KPI measurable unit", "KPI_LEAD": "Person who is responsible for updating the values of this KPI"}}, "KEY_RESULT_PAGE": {"UPDATE_KEY_RESULT": "Update Key Result", "EDIT_KEY_RESULT": "Edit Key Result", "EDIT_KEY_RESULT_PARAMETERS": "Edit Key Result Parameters", "ADD_KEY_RESULT": "Add Key Result", "UPDATE": {"STATUS": {"ON_TRACK": "on track", "NEEDS_ATTENTION": "needs attention", "OFF_TRACK": "off track", "NONE": "none"}}, "WEIGHT": {"DEFAULT": "<PERSON><PERSON><PERSON>", "INCREASE_BY_2X": "Increase 2X", "INCREASE_BY_4X": "Increase 4X", "MESSAGE": "Weights can be used to increase or decrease the importance of a single Key Result when calculating overall Objective Progress %", "OBJECTIVE_PROGRESS": "{{ weight }}% of Objective's Progress"}, "MESSAGE": {"TIME_FRAME_ENDED": "You can't update this key result now. The Time Frame for this key result has ended on {{ date }}", "TIME_FRAME_NOT_STARTED": "You can't update this key result now. The Time Frame for this key result starts on {{ date }}. Then you'll be able to update it"}, "TYPE": {"NUMERICAL": "Numerical", "TRUE_OR_FALSE": "True/False", "CURRENCY": "Валута", "TASK": "Задача", "KPI": "KPI"}, "DEADLINE": {"NO_CUSTOM_DEADLINE": "No Custom Deadline", "HARD_DEADLINE": "Hard Deadline", "HARD_AND_SOFT_DEADLINE": "Hard and Soft Deadline"}, "TOOLTIPS": {"PROGRESS": "This Key result's progress contributes to {{weight}}% of the Objective's progress", "DETAILS": "Key Result Details", "EDIT": "Edit Key Result", "WEIGHT": "Edit Weight/Type"}, "FORM": {"LABELS": {"KEY_RESULT_TYPE": "Key Result Type", "INITIAL_VALUE": "Initial value", "TARGET_VALUE": "Target value", "OWNER": "Owner", "LEAD": "Lead (optional)", "DEADLINE": "Deadline", "SOFT_DEADLINE": "Soft Deadline", "HARD_DEADLINE": "Hard Deadline", "UPDATED_VALUE": "Updated Value", "MARK_COMPLETE": "Mark as Complete", "STATUS": "Статус", "WEIGHT": "Weight", "TYPE": "Тип", "SELECT_PROJECT": "Select Project", "SELECT_TASK": "Select Task", "SELECT_KPI": "Select KPI", "ASSIGN_AS_OBJECTIVE": "Assign as objective"}, "PLACEHOLDERS": {"NAME": "Key Result Name. eg. Add Metadata to improve SEO", "DESCRIPTION": "A short description to give some context about the Key Result"}}, "HELPER_TEXT": {"KEY_RESULT_GENERAL": "A <b>Key Result</b> is how you plan to measure that you have achieved your objective .", "KEY_RESULT_OWNER": "The person responsible for completing the Key Result.", "KEY_RESULT_LEAD": "The person responsible for organizing and planning that is needed to achieve the Key Result."}}, "CANDIDATES_PAGE": {"HEADER": "Manage Candidates", "ADD_CANDIDATE": "Add Candidates", "APPLIED": "Applied", "HIRED": "<PERSON><PERSON>", "REJECTED": "Rejected", "DELETED": "Изтрит", "ARCHIVED": "Архивирана", "SELECT_EMPLOYEE_MSG": "Моля селектирайте служител от менюто по-долу.", "JOBS_CANDIDATES": "Job Candidates", "SOURCE": "Source", "RATING": "Rating", "STATUS": "Статус", "EDIT_CANDIDATE": {"HEADER": "Manage Candidate", "DEVELOPER": "Разработчик", "DEPARTMENT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "POSITION": "Позиция", "CANDIDATE_DEPARTMENTS": "Отдел на Служителя:", "EMPLOYMENT_TYPE": "Employment Type", "CANDIDATES_LEVEL": "Candidate Level", "ACCOUNT": "Account", "EMPLOYMENT": "Employment", "LOCATION": "Локация", "RATE": "Rates", "HIRING": "Hiring", "EXPERIENCE": "Experience", "SKILLS": "Skills", "ALL_FEEDBACKS": "All feedbacks", "FEEDBACKS": "Feedbacks", "EDUCATION": "Education", "SCHOOL_NAME": "School name", "DEGREE": "Degree/Diploma", "FIELD": "Field(s)", "COMPLETION_DATE": "Date of completion", "ADDITIONAL_NOTES": "Additional notes", "OCCUPATION": "Occupation", "ORGANIZATION": "Organization name", "DURATION": "Duration", "DESCRIPTION": "Description", "TASKS": "Tasks", "HISTORY": "History", "DOCUMENTS": "Documents", "DOCUMENT_NAME": "Document name", "NAME": "Име", "DOCUMENT": "Document", "FEEDBACK_DESCRIPTION": "Feedback description", "INTERVIEW_FEEDBACK": "Interview: ", "INTERVIEWER": "Interviewer", "FEEDBACK_STATUS": "Статус", "LEAVE_FEEDBACK": "Leave a feedback about the ", "STATUS": "Selected status: ", "INTERVIEW": {"INTERVIEW": "Interview", "INTERVIEWS": "Interviews", "INTERVIEWER": "Interviewer: ", "ADD_INTERVIEW": "Add interview", "HIDE_PAST": "<PERSON>de past interviews", "ON": "on", "TO": "To", "FROM": "from", "WITH": "with", "SCHEDULE_INTERVIEW": "Schedule interview", "SCHEDULED_INTERVIEWS": "Scheduled interviews", "CONTINUE": "Continue ", "PAST_DATE": "You have chosen a day in the past", "EDIT_INTERVIEW": "Edit interview", "STEP_1": "Step 1", "STEP_2": "Step 2", "STEP_3": "Step 3", "NEXT": "Next", "PREVIOUS": "Previous", "SAVE": "Запази", "CREATE_CRITERIONS": "Create criterions", "EMAIL_NOTIFICATION": "Email notification", "DELETE_INTERVIEW": "Delete interview", "DELETE_FEEDBACK": "Delete feedback", "DELETE_INTERVIEW_ARE_YOU_SURE": "Are you sure you want to delete this interview?", "DELETE_FEEDBACK_ARE_YOU_SURE": "Are you sure you want to delete this feedback?", "SUMMARY": "Summary", "DETAILS": "Send interview details to", "NOTIFY_CANDIDATE": "Notify candidate", "NOTIFY_INTERVIEWERS": "Notify interviewer(s)", "INTERVIEWERS": "interviewer(s)", "HIRE": "<PERSON>re", "RATING": "Rating", "DESCRIPTION": "Description: ", "POSTPONE": "Postpone", "REJECT": "Reject", "INTERVIEW_TITLE_EXIST": "An interview with such name already exists", "SET_AS_ARCHIVED": "{{ title }} set as archived."}}, "INTERVIEW_INFO_MODAL": {"SCHEDULED": "Scheduled", "HOURS_AGO": " hour(s) ago", "LESS_MINUTE": " less than a minute ago", "MINUTES_AGO": " minutes ago", "DAYS_AGO": " day(s) ago", "DATE": "Дата", "TIME": "Time", "CANDIDATE": "Candidate", "INTERVIEWERS": "Interviewers", "LOCATION": "Локация", "NOTE": "Note", "OF": "of", "INTERVIEWS_LOWER_CASE": "interviews"}, "ADD_CANDIDATES": {"STEP_1": "Step 1", "STEP_2": "Step 2", "STEP_3": "Step 3", "UPLOAD_CV": "Upload CV", "ADD_ANOTHER_CANDIDATE": "Add Another Candidate", "FINISHED_ADDING": "I’ve Added All Current Candidates", "NEXT": "Next", "PREVIOUS": "Previous"}, "MANAGE_INTERVIEWS": {"CALENDAR": "Calendar", "INTERVIEWS": "Interviews", "MANAGE_INTERVIEWS": "Manage Interviews", "CRITERIONS": "Criterions", "DEFAULT": "Default list", "DATE": "Дата", "SORT_BY": "Sort by", "SEARCH_BY_INTERVIEW": "Search by interview's title", "SEARCH_BY_CANDIDATE": "Search by candidate's name", "CANDIDATE_NAME": "Candi<PERSON>'s name", "RATING": "Rating", "RESET_FILTERS": "Reset filters", "TITLE": "Title", "INTERVIEWERS": "Interviewers", "ADD_FEEDBACK": "Add a feedback", "LOCATION": "Локация", "NOTES": "Бележки", "ACTIONS": "Actions", "EDIT": "Редактиране", "ARCHIVE": "Archive", "PAST": "Past", "DELETE": "Изтрий", "UPDATED": "Updated", "CANDIDATE": "Candidate", "FEEDBACK_PROHIBIT": "Adding feedback about future interviews is prohibited", "START_DATE": "Начална дата"}, "STATISTIC": {"STATISTICS": "Statistic", "RATING": "Overall rating", "INTERVIEWER_ASSESSMENT": "Rating per interview", "CRITERIONS_RATING": "Criterion's rating per interview", "CANDIDATE_CRITERIONS_RATING": "Average criterion's rating", "NO_DATA": "No data yet ", "SELECT_INTERVIEW": "Select an interview", "SELECT_INTERVIEW_INTERVIEWER": "Select an interview and interviewer", "SELECT_CANDIDATE": "Select a candidate"}, "CRITERIONS": {"CANDIDATE_CRITERIONS": "Candidates criterions", "RATE_CANDIDATE_BY_CRITERIONS": "Rate candidate by criterions", "TECHNOLOGY_STACK": "Technology Stack", "ALREADY_EXISTED": "Already existed", "TOASTR_ALREADY_EXIST": "A criterion with such name already exists", "PERSONAL_QUALITIES": "Personal Qualities", "CHOOSE_CRITERIONS": "Choose criterions for candidate", "TECHNOLOGY_PLACEHOLDER": "If you want to add criteria from the technology stack, you need to create them", "PERSONAL_QUALITIES_PLACEHOLDER": "If you want to add criteria from the personal quality list, you need to create them"}}, "ORGANIZATIONS_PAGE": {"ORGANIZATIONS": "Организации", "EMPLOYEES": "Служители", "POSITIONS": "Позиции", "EDIT_PUBLIC_PAGE": "Edit Public Page", "SELECT_ORGANIZATION": "Моля, изберете организация от менюто по-горе.", "MAIN": "Основни", "TAGS_OPTIONS": "Tags & Options", "VARIANTS": "Variants", "DESCRIPTION": "Description", "DEPARTMENTS": "Отдели", "VENDORS": "Доставчици", "VENDOR": "Доставчик", "NAME": "Име", "EXPENSE_CATEGORIES": "Expense Categories", "PROJECTS": "Проекти", "ACTIVE": "Активна", "EMPLOYMENT_TYPE": "Employment Type", "ARCHIVED": "Архивирана", "LOCATION": "Локация", "SETTINGS": "Настройки", "REGISTER_AS_EMPLOYEE": "Register as Employee", "TEAMS": "Отбори", "TEAM_NAME": "{{ name }} Team", "NOT_WORKED": "Not Worked", "ROLES": "Роли", "HELP_CENTER": "Help Center", "DOCUMENTS": "Documents", "DOCUMENTS_NO_DATA_MESSAGE": "You have not created any department.", "EXPENSE_RECURRING": "Recurring Expenses", "RECURRING_EXPENSE": "Задължителни разходи в организацията", "EMPLOYMENT_TYPES": "Employment Types", "INVITE_CONTACT": "Invite Contact", "EMAIL_INVITE": "<PERSON><PERSON>", "ADD_LEVEL_OF_EMPLOYEE": "Add level of employee", "LEVEL_OF_EMPLOYEE": "Employee Levels", "EMPLOYEE_LEVEL_NO_DATA_MESSAGE": "You have not created any employee level.", "POSITION_NO_DATA_MESSAGE": "You have not created any position.", "PHONE": "Phone", "EMAIL": "Eлектронна поща", "WEBSITE": "Website", "DOCUMENT_URL": "Document URL", "UPDATED": "Updated", "LEVEL_NAME": "Level name", "EXPENSE_NAME": "Expense name", "EMPLOYMENT_TYPE_NAME": "Employment type name", "EMPLOYMENT_TYPE_NO_DATA_MESSAGE": "You have not created any employment type.", "VENDORS_NO_DATA_MESSAGE": "You have not created any vendor.", "EXPENSE_NO_DATA_MESSAGE": "You have not created any recurring expense.", "CONTACTS": "Contacts", "LEVEL": "Level", "ORGANIZATION": "ORGANIZATION", "HOURS_WORKED": "hours worked", "CLIENTS": "Клиенти", "PROFILE": "Profile", "PORTFOLIO": "Portfolio", "BROWSE": "Browse", "SEARCH": "Search", "EDIT": {"SETTINGS_SECTION": "Settings Section", "ALL": "All", "ACCOUNTING": "Accounting", "HEADER": "Управление", "CLIENT": "Кли<PERSON><PERSON>т", "CONTACT": "Contact", "NEW_CLIENT": "Добавяне на нов клиент", "NAME": "Име", "PRIMARY_EMAIL": "Основна Е-поща", "PHONE": "Основен телефон", "COUNTRY": "Държава", "CITY": "<PERSON>р<PERSON><PERSON>", "STREET": "Улица", "PROJECTS": "Проекти", "FAX": "Fax", "FISCAL_INFORMATION": "Fiscal Information", "WEBSITE": "Website", "SECOND_ADDRESS": "Street 2", "IMAGE_URL": "Image URL", "POSTCODE": "Postcode", "DEPARTMENT_NAME": "Име на отдела", "POSITION_NAME": "Название на позиция", "NEW_PROJECT": "Добавяне на нов проект", "START_DATE": "Начална дата", "END_DATE": "Крайна дата", "BILLING": "Billing", "CURRENCY": "Валута", "OWNER": "Owner", "TEAMS": "Отбори", "ADD_NEW_CONTACT": "Add New Contact", "EDIT_CONTACT": "Edit Contact", "GENERAL_SETTINGS": "Главни Настройки", "DESIGN": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BONUS": "<PERSON>о<PERSON><PERSON><PERSON>", "INVITE": "Покани", "CLICK_EMPLOYEE": "Кликни за редакция на служителя", "EDIT_PROJECT": "Редактирай Проекта", "REGIONS": "Области", "ROLES_PERMISSIONS": "Роли и Позволения", "DATE_LIMIT": "Date Limit", "USER_ORGANIZATIONS": "{{ name }}'s List of Organizations", "ADDED_TO_ORGANIZATION": " the organization", "USER_WAS_DELETED": "'{{ name }}' was removed", "USER_WAS_REMOVED": "'{{ name }}' was removed from organization.", "EMPLOYEE_POSITION": "Employee Position", "PROJECT_URL": "Project Url", "VISIBILITY": "Visibility", "MEMBERS": "Employee/Teams", "SETTINGS": {"TIMER_SETTINGS": "Timer <PERSON>s", "ALLOW_MODIFY_TIME": "Allow Modify Time", "ALLOW_MODIFY_TIME_INFO": "Allow employee to modify manual time.", "ALLOW_DELETE_TIME": "Allow Delete Time", "ALLOW_DELETE_TIME_INFO": "Allow employee to delete time.", "ALLOW_MANUAL_TIME": "Allow Manual Time", "ALLOW_MANUAL_TIME_INFO": "Allow employee to add manual time.", "REQUIRE_REASON": "Require Reason", "REQUIRE_REASON_INFO": "Reason of the add manual time or edit time logs.", "REQUIRE_DESCRIPTION": "Require Description", "REQUIRE_DESCRIPTION_INFO": "Description of the tracked time will be required when timer starting and also for the time logs adding or editing manual time.", "REQUIRE_PROJECT": "Require Project", "REQUIRE_PROJECT_INFO": "Project of the tracked time will be required when timer starting and also for the time logs adding or editing manual time.", "REQUIRE_TASK": "Require Task", "REQUIRE_TASK_INFO": "Task of the tracked time will be required when timer starting and also for the time logs adding or editing manual time.", "REQUIRE_CLIENT": "Require Client", "REQUIRE_CLIENT_INFO": "Client of the tracked time will be required when timer starting and also for the time logs adding or editing manual time.", "ALLOW_TO_SET_DEFAULT_ORGANIZATION": "Allow user to set default organization", "INACTIVITY_TIME_LIMIT": "Inactivity Limit", "INACTIVITY_TIME_LIMIT_INFO": "Inactivity tracking timeout (i.e., how many seconds of inactivity is allowed)", "ACTIVITY_PROOF_DURATION": "Activity proof duration", "ACTIVITY_PROOF_DURATION_INFO": "Duration of activity proof countdown dialog, it represent an amount of seconds given to prove activity", "ALLOW_TRACK_INACTIVITY": "Allow to track inactivity", "PROOF_OF_COMPLETION": "Доказателство за завършване", "PROOF_OF_COMPLETION_TYPE": "Тип доказателство за завършване", "LINKED_ISSUE": "Свързан проблем", "COMMENT": "Коментар", "HISTORY": "История", "ACCEPTANCE_CRITERIA": "Критерии за приемане", "DRAFT_ISSUE": "Проблеми с чернови", "PROOF_OF_COMPLETION_TYPE_DROPDOWN": {"NONE": "Няма", "PRIVATE": "<PERSON>а<PERSON><PERSON><PERSON>н", "PUBLIC": "Обществено"}, "NOTIFY_TASK_LEFT": "Известие за оставена задача", "NOTIFY_TASK_LEFT_PERIOD": "Период на задачата за известяване (в дни)", "AUTO_CLOSE_ISSUE": "Автоматично затваряне на проблем", "AUTO_CLOSE_ISSUE_PERIOD": "Период на издаване на автоматично затваряне (в дни)", "AUTO_ARCHIVE_ISSUE": "Проблем с автоматичното архивиране", "AUTO_ARCHIVE_ISSUE_PERIOD": "Период на издаване на автоматично архивиране (в дни)", "AUTO_STATUS": "Автоматично състояние"}, "TEAMS_PAGE": {"MANAGERS": "Managers", "MEMBERS": "Members"}}, "PERMISSIONS": {"ADMIN_DASHBOARD_VIEW": "Виж Административното Табло", "TEAM_DASHBOARD": "Вижте таблото за управление на екипа", "PROJECT_MANAGEMENT_DASHBOARD": "Преглед на таблото за управление на проекти", "TIME_TRACKING_DASHBOARD": "Вижте таблото за проследяване на времето", "ACCOUNTING_DASHBOARD": "Преглед на счетоводното табло", "HUMAN_RESOURCE_DASHBOARD": "Вижте таблото за управление на човешките ресурси", "ORG_PAYMENT_VIEW": "View Payments", "ORG_PAYMENT_ADD_EDIT": "Create/Edit/Delete Payments", "ORG_EXPENSES_VIEW": "Виж всички разходи", "ORG_EXPENSES_EDIT": "Създай/Редактирай/Изтрии разходи", "EMPLOYEE_EXPENSES_VIEW": "View All Employee Expenses", "EMPLOYEE_EXPENSES_EDIT": "Create/Edit/Delete Employee Expenses", "ORG_INCOMES_EDIT": "Създай/Редактирай/Изтрии приходи", "ORG_INCOMES_VIEW": "Виж всички приходи", "ORG_PROPOSALS_EDIT": "Create/Edit/Delete Proposals Register", "ORG_PROPOSALS_VIEW": "Виж страницата с кандидатствания", "ORG_PROPOSAL_TEMPLATES_VIEW": "View Proposal Templates Page", "ORG_PROPOSAL_TEMPLATES_EDIT": "Create/Edit/Delete Proposal Templates", "ORG_TIME_OFF_VIEW": "Виж Time Off страницата", "ORG_EMPLOYEES_VIEW": "Преглед на служителите в организацията", "ORG_EMPLOYEES_EDIT": "Създай/Редактира<PERSON>/Изтрии служители в организацията", "ORG_CANDIDATES_VIEW": "View Organization Candidates", "ORG_CANDIDATES_EDIT": "Create/Edit/Delete Organization Candidates", "ORG_USERS_VIEW": "Виж потребителите в организацията", "ORG_USERS_EDIT": "Създай/Редактира<PERSON>/Изтрии потребители на организацията", "ORG_INVITE_VIEW": "Виж покани на организацията", "ORG_INVITE_EDIT": "Създай/Преизпрати/Изтрии покани", "ORG_CANDIDATES_DOCUMENTS_VIEW": "View All Candidates Documents", "ORG_CANDIDATES_TASK_EDIT": "Create/Edit Task", "ORG_CANDIDATES_INTERVIEW_EDIT": "Create/Edit Interview", "ORG_CANDIDATES_INTERVIEW_VIEW": "View Interview", "ORG_INVENTORY_PRODUCT_EDIT": "Management Product", "ORG_TAGS_ADD": "Create Tags", "ORG_TAGS_VIEW": "View Tags", "ORG_TAGS_EDIT": "Edit Tags", "ORG_TAGS_DELETE": "Delete Tags", "ORG_CANDIDATES_FEEDBACK_EDIT": "Create/Edit/Delete Candidate Feedback", "ALL_ORG_VIEW": "Виж всички организации", "ALL_ORG_EDIT": "Създай/Редактирай/Изтрии всички организации", "POLICY_VIEW": "Виж Time Off политиката", "POLICY_EDIT": "Редактирай Time Off политиката", "CHANGE_SELECTED_EMPLOYEE": "Промени избрания служител", "CHANGE_SELECTED_CANDIDATE": "Change Selected Candidate", "CHANGE_SELECTED_ORGANIZATION": "Промени избраната организация", "CHANGE_ROLES_PERMISSIONS": "Промени ролите и Позволенията", "ACCESS_PRIVATE_PROJECTS": "Access Private Projects", "TIMESHEET_EDIT_TIME": "Edit Time in Timesheet", "INVOICES_VIEW": "View Invoices", "INVOICES_EDIT": "Edit Invoices Add", "ESTIMATES_VIEW": "View Estimates", "ESTIMATES_EDIT": "Edit Estimates Add", "EDIT_SALES_PIPELINES": "Edit Sales Pipelines", "VIEW_SALES_PIPELINES": "View Sales Pipelines", "APPROVALS_POLICY_EDIT": "Edit Approvals Policy", "APPROVALS_POLICY_VIEW": "View Approvals Policy", "REQUEST_APPROVAL_EDIT": "Edit Approval Request", "REQUEST_APPROVAL_VIEW": "View Approval Request", "ORG_CANDIDATES_INTERVIEWERS_EDIT": "Create/Edit Interviewers", "ORG_CANDIDATES_INTERVIEWERS_VIEW": "View Interviewers", "VIEW_ALL_EMAILS": "View All Emails", "VIEW_ALL_EMAIL_TEMPLATES": "View All Emails Templates", "ORG_HELP_CENTER_EDIT": "Edit Organization Help Center", "PUBLIC_PAGE_EDIT": "Edit Organization Public Page", "CAN_APPROVE_TIMESHEET": "Approve Timesheet", "EVENT_TYPES_VIEW": "View Event Types", "TIME_OFF_EDIT": "Edit Time Off", "ORG_INVENTORY_VIEW": "View Organization Inventory", "INVENTORY_GALLERY_VIEW": "View Inventory Gallery", "INVENTORY_GALLERY_EDIT": "Edit Inventory Gallery", "ORG_EQUIPMENT_VIEW": "View Organization Equipment", "ORG_EQUIPMENT_EDIT": "Edit Organization Equipment", "ORG_EQUIPMENT_SHARING_VIEW": "View Organization Equipment Sharing", "ORG_EQUIPMENT_SHARING_EDIT": "Edit Organization Equipment Sharing", "EQUIPMENT_MAKE_REQUEST": "Request Make Equipment Make", "EQUIPMENT_APPROVE_REQUEST": "Request Approve Equipment", "ORG_PRODUCT_TYPES_VIEW": "View Organization Product Types", "ORG_PRODUCT_TYPES_EDIT": "Edit Organization Product Types", "ORG_PRODUCT_CATEGORIES_VIEW": "View Organization Product Categories", "ORG_PRODUCT_CATEGORIES_EDIT": "Edit Organization Product Categories", "VIEW_ALL_ACCOUNTING_TEMPLATES": "View All Accounting Templates", "GROUPS": {"GENERAL": "Основни", "ADMINISTRATION": "Администрация"}, "ONLY_ADMIN": "These permissions are read-only and enabled only for admin", "INSUFFICIENT": "You do not have sufficient permissions. The following permissions are missing:", "ORG_SPRINT_EDIT": "Create/Edit Sprints", "ORG_SPRINT_VIEW": "View Sprints", "ORG_PROJECT_EDIT": "Create/Edit Projects", "ORG_CONTACT_EDIT": "Create/Edit Contacts", "ORG_CONTACT_VIEW": "View Contacts", "ORG_TEAM_ADD": "Add Teams", "ORG_TEAM_VIEW": "View Teams", "ORG_TEAM_EDIT_ACTIVE_TASK": "Редактиране на активни задачи", "ORG_TEAM_EDIT": "Edit Teams", "ORG_TEAM_DELETE": "Delete Teams", "ORG_TEAM_JOIN_REQUEST_VIEW": "Преглед на заявките за присъединяване към екипи", "ORG_TEAM_JOIN_REQUEST_EDIT": "Изтриване на заявки за присъединяване към екипи", "ORG_CONTRACT_EDIT": "Create/Edit Contracts", "TIME_TRACKER": "Access Time Tracker", "TENANT_ADD_EXISTING_USER": "Tenant Add User To Organization", "INTEGRATION_VIEW": "View Integrations", "FILE_STORAGE_VIEW": "View File Storage", "PAYMENT_GATEWAY_VIEW": "View Payment Gateway", "SMS_GATEWAY_VIEW": "View SMS Gateway", "CUSTOM_SMTP_VIEW": "View Custom SMTP", "IMPORT_EXPORT_VIEW": "View Import/Export", "ORG_JOB_EMPLOYEE_VIEW": "View Job Employees", "ORG_JOB_MATCHING_VIEW": "View Job Matching", "ACCESS_DELETE_ACCOUNT": "Access Delete Account", "ACCESS_DELETE_ALL_DATA": "Access Delete All Data", "TENANT_SETTING": "Create/Edit/Delete tenant settings", "ALLOW_DELETE_TIME": "Allow Delete Time", "ALLOW_MODIFY_TIME": "Allow Modify Time", "ALLOW_MANUAL_TIME": "Allow Manual Time", "DELETE_SCREENSHOTS": "Allow Delete Screenshot", "ORG_TASK_SETTING": "Настройки на задачите", "ORG_MEMBER_LAST_LOG_VIEW": "Преглед на последния запис"}, "BILLING": "Billing", "BUDGET": "Budget", "OPEN_SOURCE": "Open-Source", "ORGANIZATION_ADD": "Add Organization", "IMAGE": "Снимка", "SPRINTS": "Sprints", "NO_IMAGE": "Image no available"}, "CONTACTS_PAGE": {"VISITORS": "Visitors", "LEADS": "Leads", "CUSTOMERS": "Customers", "CLIENTS": "Клиенти", "CITY": "<PERSON>р<PERSON><PERSON>", "STREET": "Улица", "COUNTRY": "Държава", "PROJECTS": "Проекти", "EMAIL": "Основна Е-поща", "PHONE": "Основен телефон", "CONTACT_TYPE": "Contact type", "MAIN": "Основни", "ADDRESS": "Address", "ADDRESS_2": "Address 2", "MEMBERS": "Members", "BUDGET": "Budget"}, "PUBLIC_PAGE": {"LANGUAGES": "Languages", "AWARDS": "Awards", "COMPANY_SKILLS": "Company Skills", "SKILLS": "Skills", "PROFILE": "Profile", "PORTFOLIO": "Portfolio", "OVERVIEW": "Overview", "DESCRIPTION": "Description", "TOTAL_BONUSES_PAID": "Total Bonuses Paid", "COMPANY_PROFILE": "Company Profile", "TOTAL_CLIENTS": "Total Clients", "YEAR_FOUNDED": "Year Founded", "COMPANY_SIZE": "Company Size", "CLIENT_FOCUS": "Client Focus", "MONTHLY_INCOME": "Monthly Income", "TOTAL_INCOME": "Общ доход", "TOTAL_PROJECTS": "Total Projects", "MINIMUM_PROJECT_SIZE": "Minimum Project Size", "EMPLOYEES": "Служители", "PROFITS": "Profits", "RATE": "Почасово", "ACTIVE": "Акти<PERSON><PERSON>н", "STARTED_WORK_ON": "Started work on", "PAY_PERIOD": "Pay period", "AVERAGE_BONUS": "Average Bonus", "AVERAGE_EXPENSES": "Average Expenses", "AVERAGE_INCOME": "Average Income", "EMPLOYEE_UPDATED": "Employee has been updated.", "IMAGE_UPDATED": "The image has been updated.", "FAIL_TO_UPDATE_IMAGE": "Update failed.", "ANONYMOUS": "Anonymous"}, "PROPOSALS_PAGE": {"HEADER": "Предложения - управление", "STATISTICS": "Статистика", "ACCEPTED_PROPOSALS": "Приети предложения", "TOTAL_PROPOSALS": "Общо предложения", "SUCCESS_RATE": "Успеваемост", "PROPOSALS": "Proposals", "REGISTER": {"REGISTER_PROPOSALS": "Register Proposal", "AUTHOR": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TEMPLATE": "Template", "JOB_POST_URL": "Job Post URL", "PICK_A_DATE": "Изберете дата", "PROPOSAL_DATE": "Proposal Date", "JOB_POST_CONTENT": "Job Post Content", "UPLOAD": "Upload", "PROPOSALS_CONTENT": "Proposal Content", "REGISTER_PROPOSALS_BUTTON": "Register Proposal"}, "PROPOSAL_DETAILS": {"PROPOSAL_DETAILS": "Proposal Details", "EDIT": "Редактиране", "AUTHOR": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "JOB_POST_URL": "Job Post URL", "PROPOSAL_SENT_ON": "Proposal Sent On", "STATUS": "Статус", "JOB_POST_CONTENT": "Job Post Content", "PROPOSAL_CONTENT": "Proposal Content", "VIEW_JOB_POST": "Преглед на публикация"}, "EDIT_PROPOSAL": {"EDIT_PROPOSAL": "Edit Proposal", "AUTHOR": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "JOB_POST_URL": "Job Post URL", "PROPOSAL_SENT_ON": "Proposal Sent On", "JOB_POST_CONTENT": "Job Post Content", "PROPOSAL_CONTENT": "Proposal Content", "EDIT_PROPOSAL_BUTTON": "Edit Proposal", "PLACEHOLDER": {"JOB_POST_URL": "Job Post URL"}}}, "APPROVAL_REQUEST_PAGE": {"APPROVAL_REQUEST_NAME": "Име", "APPROVAL_REQUEST_TYPE": "Тип", "APPROVAL_REQUEST_MIN_COUNT": "Min Count", "APPROVAL_REQUEST_APPROVAL_POLICY": "Approval Policy", "APPROVAL_REQUEST_STATUS": "Статус", "APPROVAL_REQUEST_ACTIONS": "Actions", "CREATED_BY": "Created By", "APPROVE": "Approve", "REFUSE": "Refuse", "HEADER": "Approval Request", "EMPLOYEES": "Служители", "TEAMS": "Отбори", "APPROVAL_POLICY": "Approval Policy", "CHOOSE_POLICIES": "Choose policies/s", "EDIT_APPROVAL_REQUEST": "Edit Request Approval", "ADD_APPROVAL_REQUEST": "Add Request Approval", "APPROVAL_REQUEST_CREATED": "Request Approval '{{ name }}' was added", "APPROVAL_REQUEST_UPDATED": "Request Approval '{{ name }}' was changed", "APPROVAL_REQUEST_DELETED": "Request Approval '{{ name }}' was removed", "APPROVAL_SUCCESS": "Approval '{{ name }}' was approved", "REFUSE_SUCCESS": "Approval '{{ name }}' was refused", "APPROVED": "Approved", "REFUSED": "Refused", "REQUESTED": "Requested", "ACTION": "Action", "CREATED_AT": "Created At"}, "APPROVAL_POLICY_PAGE": {"EDIT_APPROVAL_POLICY": "Edit Approval Policy", "ADD_APPROVAL_POLICY": "Add Approval Policy", "HEADER": "Approval Policy", "APPROVAL_POLICY_NAME": "Име", "APPROVAL_POLICY_TYPE": "Тип", "APPROVAL_POLICY_DESCRIPTION": "Description", "BUSINESS_TRIP": "Business Trip", "EQUIPMENT_SHARING": "Equipment Sharing", "TIME_OFF": "Time Off"}, "TIME_OFF_PAGE": {"HEADER": "Time Off", "REQUEST": "Request", "REQUEST_TIME_OFF": "Request Time Off", "EDIT": "Редактиране", "ADD_HOLIDAYS": "Add Holidays", "DISPLAY_HOLIDAYS": "Display Holidays", "HOLIDAY_NAME": "Holiday name", "SELECT_EMPLOYEES": "Select Employees", "SETTINGS": "Настройки", "EDI": "Edit Time Off record", "SELECT_HOLIDAY_NAME": "Select Holiday name", "ADD_OR_REMOVE_EMPLOYEES": "Добави или премахни служители", "SELECT_TIME_OFF_POLICY": "Select Time-off Policy", "ADD_A_DESCRIPTION": "Add a description", "DESCRIPTION": "Description", "START_DATE": "Начална дата", "END_DATE": "Крайна дата", "REQUEST_DATE": "Request Date", "STATUS": "Статус", "TIME_OFF_REQUEST": "Time off request", "VIEW_REQUEST_DOCUMENT": "View Request Document", "MULTIPLE_EMPLOYEES": "Multiple employees", "UPLOAD_REQUEST_DOCUMENT": "Upload Request Document", "STATUSES": {"REQUESTED": "Requested", "APPROVED": "Approved", "DENIED": "Отказан", "ALL": "All"}, "ACTIONS": {"EDIT": "Edit Time Off record", "APPROVE_DAYS_OFF_REQUEST": "Approve Time Off Request", "DENY_DAYS_OFF_REQUEST": "Deny Time Off Request", "DELETE_DAYS_OFF_REQUEST": "Delete Time Off Request"}, "POLICY": {"HEADER": "Time Off Policy", "POLICY": "Policy", "ADD_POLICY": "Add Policy", "EDIT_POLICY": "Edit Policy", "NAME": "Име", "REQUIRES_APPROVAL": "Requires Approval", "PAID": "Paid", "NAME_IS_REQUIRED": "Policy name is required!"}, "NOTIFICATIONS": {"NO_CHANGES": "No changes", "STATUS_SET_APPROVED": "You successfully set the time off request status to approved.", "ERR_SET_STATUS": "Unable to set time off request status.", "APPROVED_NO_CHANGES": "The time off request status is already set to approved", "RECORD_CREATED": "Time off record was saved", "REQUEST_DENIED": "You successfully set the time off request status to denied", "DENIED_NO_CHANGES": "The time off request status is already set to denied", "REQUEST_DELETED": "Time off request was removed", "ERR_LOAD_RECORDS": "Unable to load time off records", "ERR_DELETE_REQUEST": "Unable to delete Time off request", "ERR_CREATE_RECORD": "Unable to create Time off record", "REQUEST_UPDATED": "Time Off request successfully updated", "ERR_UPDATE_RECORD": "Unable to update Time off record"}}, "TAGS_PAGE": {"HEADER": "Тагове", "ADD_TAGS": "Добавяне на таг", "EDIT_TAGS": "Редактиране на таг", "TAGS_NAME": "Име", "TAGS_DESCRIPTION": "Description", "TAGS_COLOR": "Color", "TAGS_ADD_TAG": "Tag '{{ name }}' was added", "TAGS_EDIT_TAG": "Tag '{{ name }}' was changed", "TAGS_DELETE_TAG": "Tag '{{ name }}' was removed", "TAGS_SELECT_NAME": "Tag name", "TAGS_SELECT_COLOR": "Tag color", "TAGS_SELECT_DESCRIPTION": "Tag description", "ADD_NEW_TAG": "Add new Tag", "TENANT_LEVEL": "Tenant level", "TAGS_TYPE": "Tags type"}, "SKILLS_PAGE": {"HEADER": "Skills"}, "LANGUAGE_PAGE": {"HEADER": "Languages", "ADD_NEW_LANGUAGE": "Add new Language"}, "LANGUAGE_LEVELS": {"CONVERSATIONAL": "Conversational", "NATIVE": "Native", "FLUENT": "Fluent"}, "EQUIPMENT_PAGE": {"HEADER": "Equipment", "ADD_EQUIPMENT": "Add Equipment", "EDIT_EQUIPMENT": "Edit Equipment", "EQUIPMENT_NAME": "Име", "EQUIPMENT_TYPE": "Тип", "EQUIPMENT_SN": "SN", "EQUIPMENT_MANUFACTURED_YEAR": "Manufactured year", "EQUIPMENT_INITIAL_COST": "Initial cost", "EQUIPMENT_CURRENCY": "Валута", "EQUIPMENT_MAX_SHARE_PERIOD": "Max share period", "EQUIPMENT_AUTO_APPROVE": "Auto approve", "EQUIPMENT_EDITED": "Equipment edited", "EQUIPMENT_DELETED": "Equipment '{{ name }}' was removed", "EQUIPMENT_ADDED": "Equipment added", "EQUIPMENT_SAVED": "Equipment '{{ name }}' was saved", "CURRENCY": "Валута"}, "EQUIPMENT_SHARING_PAGE": {"HEADER": "Equipment Sharing", "ADD_EQUIPMENT_REQUEST": "Add Equipment Request", "EDIT_EQUIPMENT_REQUEST": "Edit Equipment Request", "DELETE_EQUIPMENT_REQUEST": "Delete Equipment Request", "REQUEST": "Request", "EQUIPMENT_NAME": "Equipment name", "EQUIPMENT_SHARING_POLICY": "Equipment Sharing Policy", "SHARE_REQUEST_DATE": "Share request date", "SHARE_START_DATE": "Share start date", "SHARE_END_DATE": "Share end date", "CREATED_BY": "Created By", "STATUS": "Статус", "REQUEST_SAVED": "Request was saved", "REQUEST_DELETED": "Request was removed", "MESSAGES": {"BEFORE_REQUEST_DAY_ERR": "Date must be after request day.", "EXCEED_PERIOD_ERR": "The maximum allowed days for this item are ", "BEFORE_START_DATE_ERR": "End date cannot be before start date. ", "ITEM_RETURNED_BEFORE_ERR": "Item should be returned before "}, "ACTIONS": "Actions", "APPROVE": "Approve", "REFUSE": "Refuse", "APPROVAL_SUCCESS": "Request Approval Success", "REFUSE_SUCCESS": "Request Refuse Success", "APPROVED": "Approved", "REFUSED": "Refused", "REQUESTED": "Requested", "ACTION": "Action"}, "EQUIPMENT_SHARING_POLICY_PAGE": {"HEADER": "Equipment Sharing Policy", "ADD_EQUIPMENT_SHARING_POLICY": "Add Equipment Sharing Policy", "EDIT_EQUIPMENT_SHARING_POLICY": "Edit Equipment Sharing Policy", "DELETE_EQUIPMENT_SHARING_POLICY": "Delete Equipment Sharing Policy", "REQUEST": "Request", "EQUIPMENT_SHARING_POLICY_NAME": "Equipment Sharing Policy name", "EQUIPMENT_SHARING_POLICY_ORG": "Equipment Sharing Policy organization", "EQUIPMENT_SHARING_POLICY_DESCRIPTION": "Equipment Sharing Policy description", "REQUEST_SAVED": "Policy saved", "REQUEST_DELETED": "Policy deleted", "ACTIONS": "Actions", "MESSAGES": {"EQUIPMENT_REQUEST_SAVED": "Request '{{ name }}' was changed", "EQUIPMENT_REQUEST_DELETED": "Request '{{ name }}' was removed"}}, "INVENTORY_PAGE": {"HEADER": "Inventory", "ADD_INVENTORY_ITEM": "Add inventory item", "EDIT_INVENTORY_ITEM": "Edit inventory item", "INVENTORY_ITEM_DELETED": "Inventory '{{ name }}' was removed", "INVENTORY_ITEM_SAVED": "Inventory '{{ name }}' was saved", "EDIT_PRODUCT_VARIANT": "Edit product variant", "PRODUCT_VARIANT_SAVED": "Product variant saved", "NAME": "Име", "ENABLED": "Enabled", "PRODUCT_TYPE": "Product type", "PRODUCT_CATEGORY": "Product category", "IS_SUBSCRIPTION": "Is subscription", "IS_PURCHASE_AUTOMATICALLY": "Is purchased automatically", "CAN_BE_SOLD": "Can be sold", "CAN_BE_PURCHASED": "Can be purchased", "CAN_BE_CHARGED": "Can be charged", "CAN_BE_RENTED": "Can be rented", "IS_EQUIPMENT": "Is equipment", "TRACK_INVENTORY": "Track inventory", "ADD_OPTION": "Add option", "EDIT_OPTION": "Edit option", "INTERNATIONAL_REFERENCE": "International reference", "CODE": "Code", "NOTES": "Бележки", "DESCRIPTION": "Description", "UNIT_COST": "Unit Cost", "UNIT_COST_CURRENCY": "Unit cost currency", "RETAIL_PRICE": "Retail price", "RETAIL_PRICE_CURRENCY": "Retail price currency", "QUANTITY": "Quantity", "TAXES": "Taxes", "BILLING_INVOICING_POLICY": "Billing invoicing policy", "PRODUCT_TYPES": "Product types", "PRODUCT_CATEGORIES": "Product categories", "ORGANIZATION": "Organization", "EDIT_PRODUCT_TYPE": "Edit product type", "ADD_PRODUCT_TYPE": "Add product type", "PRODUCT_TYPE_SAVED": "Product type '{{ name }}' was saved", "PRODUCT_TYPE_DELETED": "Product type '{{ name }}' was removed", "EDIT_PRODUCT_CATEGORY": "Edit product category", "ADD_PRODUCT_CATEGORY": "Add product category", "PRODUCT_CATEGORY_SAVED": "Product category '{{ name }}' was saved", "PRODUCT_CATEGORY_DELETED": "Product category '{{ name }}' was removed", "IMAGE": "Снимка", "LANGUAGE": "<PERSON>зи<PERSON> (Language)", "PRODUCT_VARIANT_DELETED": "Product variant deleted!", "ICON": "Icon", "ADD_VARIANT": "Add variant", "EDIT_VARIANT": "Edit variant", "NO_OPTIONS_LABEL": "(no options)", "OPTIONS": "Options", "SELECT_OR_UPLOAD_IMAGE": "Select or upload image", "SELECT_IMAGE": "Select image", "NO_IMAGE_SELECTED": "No image selected", "URL": "Url", "DIMENSIONS": "Dimensions", "FEATURED_IMAGE_WAS_SAVED": "Featured image was saved!", "IMAGE_SAVED": "Image was saved!", "ADD_GALLERY_IMAGE": "Add gallery image", "SET_FEATURED_IMAGE": "Set featured image", "VIEW_GALLERY": "View gallery", "EDIT_IMAGE": "Edit gallery image", "DELETE_IMAGE": "Delete image", "IMAGE_ASSET_DELETED": "Image asset deleted", "CATEGORY": "Категория", "TYPE": "Тип", "VIEW_INVENTORY_ITEM": "View inventory item", "TAGS": "Тагове", "PRICE": "Price", "SAVE": "Запази", "CANCEL": "Отказ", "WIDTH": "<PERSON><PERSON><PERSON>", "HEIGHT": "Height", "IMAGE_ADDED_TO_GALLERY": "Image was added to the gallery", "IMAGES_ADDED_TO_GALLERY": "The Images were added to the gallery", "IMAGE_ASSET_UPDATED": "The image asset was updated", "EDIT_IMAGE_ASSET": "Edit image asset", "WAREHOUSES": "Warehouses", "EMAIL": "Eлектронна поща", "ACTIVE": "Акти<PERSON><PERSON>н", "INACTIVE": "Inactive", "LOCATION": "Локация", "ADDRESS": "Address", "CREATE_WAREHOUSE": "Create Warehouse", "EDIT_WAREHOUSE": "Edit Warehouse", "WAREHOUSE_CREATED": "Warehouse created", "COULD_NOT_CREATE_WAREHOUSE": "Could not create warehouse", "WAREHOUSE_WAS_CREATED": "Warehouse '{{ name }}' was created", "WAREHOUSE_WAS_DELETED": "Warehouse '{{ name }}' was deleted", "WAREHOUSE_WAS_UPDATED": "Warehouse '{{ name }}' was updated", "CITY": "<PERSON>р<PERSON><PERSON>", "LOGO": "Logo", "CONTACT": "Contact", "COUNTRY": "Държава", "NEW_OPTION_GROUP": "New option group", "OPTION_GROUP_NAME": "Option group name", "OPTION_TRANSLATIONS": "Option translations", "ADD_PRODUCTS": "Add products", "MANAGE_VARIANTS_QUANTITY": "Manage variants", "ADD_PRODUCT": "Add product", "STORES": "Stores", "ADD_STORE": "Add store", "EDIT_STORE": "Edit store", "CREATE_STORE": "Create store", "PHONE": "Phone", "FAX": "Fax", "FISCAL_INFORMATION": "Fiscal information", "WEBSITE": "Website", "MERCHANTS": "Merchants", "CREATE_MERCHANT": "New Merchant", "DELETE_MERCHANT": "Delete Merchant", "EDIT_MERCHANT": "Edit Merchant", "MERCHANT_CREATED_SUCCESSFULLY": "Merchant '{{ name }}' created successfully!", "MERCHANT_DELETED_SUCCESSFULLY": "Merchant '{{ name }}' deleted!", "MERCHANT_UPDATED_SUCCESSFULLY": "Merchant '{{ name }}' updated successfully!", "THIS_FIELD_IS_REQUIRED": "This field is required", "EMAIL_WRONG_FORMAT": "Email is in wrong format", "PHONE_WRONG_FORMAT": "Phone is in wrong format", "SELECTED": "Selected", "SUCCESSFULLY_ADDED_PRODUCTS": "Products were added successfully!", "MAIN": "Основни", "INVENTORY": "Inventory", "IMAGE_WAS_DELETED": "Image was deleted"}, "TASKS_PAGE": {"HEADER": "Tasks", "MY_TASK_HEADER": "My Tasks", "TEAM_TASKS_HEADER": "Team's Tasks", "ADD_TASKS": "Add Tasks", "EDIT_TASKS": "Edit Tasks", "EDIT_TASK": "Edit Task", "DELETE_TASK": "Delete Task", "TASKS_TITLE": "Title", "TASKS_DESCRIPTION": "Description", "TASKS_LOADED": "Tasks loaded", "TASK_ADDED": "Task was added", "TASK_UPDATED": "Task was changed", "TASK_DELETED": "Task was removed", "TASKS_PROJECT": "Проект", "TASKS_CREATOR": "Created By", "TASK_MEMBERS": "Служители", "TASK_ASSIGNED_TO": "Assigned To", "TASK_TEAMS": "Отбори", "TASK_ID": "ID", "TASK_NUMBER": "Task Number", "DUE_DATE": "Due Date", "ESTIMATE": "Estimate", "ESTIMATE_DAYS": "Days", "ESTIMATE_HOURS": "Hours", "ESTIMATE_MINUTES": "<PERSON>s", "TASKS_STATUS": "Статус", "TASK_PRIORITY": "Приоритет", "TASK_SIZE": "Размер", "TODO": "Todo", "IN_PROGRESS": "In Progress", "FOR_TESTING": "For Testing", "COMPLETED": "Completed", "TASK_VIEW_MODE": "Task View Mode", "PROJECT": "Проект", "COMPLETE_SPRINT": "Complete Sprint", "DATE_START": "Date Start", "DATE_END": "Date End", "BACKLOG": "Backlog", "EDIT_SPRINT": "Edit Sprint", "DELETE_SPRINT": "Delete Sprint", "SELECT": "Select", "SPRINTS_SETTINGS": "Sprints Settings", "ARE_YOU_SURE": "Are you sure you want delete sprint", "SETTINGS": "Настройки"}, "JOBS": {"EMPLOYEE": "Служител", "TITLE": "Title", "DESCRIPTION": "Description", "CREATED_DATE": "Created Date", "STATUS": "Статус", "ACTION": "Action", "APPLY": "Кандидатствайте ръчно", "APPLY_AUTO": "Автоматично прилагане", "CLOSED": "Closed", "OPEN": "Open", "APPLIED": "Applied", "COMPLETED": "Completed", "VIEW": "View", "HIDE": "<PERSON>de", "NO_JOBS": "No Jobs found", "JOB_SEARCH": "Job Search", "JOB_DETAILS": "Подробности за работата", "LOAD_MORE": "Load More", "HIDE_ALL_CONFIRM": "Are you sure you want to Hide All jobs?", "ACTIONS": "Actions", "UPWORK": "Upwork", "WEB": "Web", "HOURLY": "Почасово", "FIXED": "<PERSON>ик<PERSON><PERSON><PERSON><PERSON><PERSON>", "FILTER": {"TITLE": "Advanced Filter", "SOURCE": "Source", "JOB_TYPE": "Job Type", "JOB_STATUS": "Job Status", "BUDGET": "Budget", "LESS_THAN": "Less than"}, "BROWSE": "Browse", "SEARCH": "Search", "HISTORY": "History", "EMPLOYEES": "Служители", "MATCHINGS": "Matchings", "PROPOSALS_TEMPLATE": "Proposals Template"}, "JOB_MATCHING": {"CONFIGURE_EMPLOYEES_TO_JOBS_MATCHING": "Configure Employees to Jobs Matching", "SOURCE": "Source", "PRESET": "Preset", "KEYWORDS": "Keywords", "CATEGORY": "Категория", "OCCUPATION": "Occupation", "ADD_NEW_CRITERIONS": "Add New", "FIX_PRICE": "Fix Price", "HOURLY": "Hourly", "SAVE": "Запази", "DELETE": "Изтрий", "CRITERIONS": "Criterions", "DELETE_CRITERION_MESSAGE": "Are you sure that, you want to delete criterion", "SAVE_PRESET_MESSAGE": "Criterions will save for job preset. Are you sure you want to save?"}, "JOB_EMPLOYEE": {"EMPLOYEE": "Служител", "EMPLOYEES": "Служители", "AVAILABLE_JOBS": "Available Jobs", "APPLIED_JOBS": "Applied Jobs", "JOB_SEARCH_STATUS": "Job Search Status", "BROWSE": "Browse", "SEARCH": "Search", "HISTORY": "History", "BILLING_RATE": "Таксуване", "MINIMUM_BILLING_RATE": "Минимална фактурна ставка"}, "PROPOSAL_TEMPLATE": {"PROPOSAL_TEMPLATE": "Proposal Template", "EDIT_PROPOSAL_TEMPLATE": "Edit Proposal Template", "ADD_PROPOSAL_TEMPLATE": "Add Proposal Template", "SELECT_PROPOSAL_TEMPLATE": "Select Proposal Template", "SELECT_EMPLOYEE": "Select employee", "NAME": "Име", "CONTENT": "Content", "EMPLOYEE": "Служител", "DESCRIPTION": "Description", "IS_DEFAULT": "<PERSON>", "CONFIRM_DELETE": "Are you sure that, you want to delete", "PROPOSAL_CREATE_MESSAGE": "Proposal template '{{ name }}' was added", "PROPOSAL_EDIT_MESSAGE": "Proposal template '{{ name }}' was changed", "PROPOSAL_DELETE_MESSAGE": "Proposal template '{{ name }}' was removed", "PROPOSAL_MAKE_DEFAULT_MESSAGE": "Proposal template '{{ name }}' set as default", "YES": "Yes", "NO": "No", "BROWSE": "Browse", "SEARCH": "Search"}, "SPRINTS_PAGE": {"SPRINT_ADDED": "Sprint added", "SPRINT_UPDATED": "Sprint updated", "SPRINT_DELETED": "Sprint deleted", "SPRINT": "Sprint", "ADD_SPRINT_NAME": "Add sprint name"}, "USERS_PAGE": {"HEADER": "Manage Users", "ADD_USER": "Add User", "ADD_EXISTING_USER": "Add Existing User", "ADD_EXISTING_ORGANIZATION": "Add Existing Organization", "ADD_EXISTING_USER_TOOLTIP": "Add user from other organization", "ROLE": {"SUPER_ADMIN": "Super Admin", "ADMIN": "Администратор", "MANAGER": "Manager", "DATA_ENTRY": "Data Entry", "VIEWER": "Viewer", "EMPLOYEE": "Служител", "CANDIDATE": "Candidate", "ROLE": "Роля"}, "EDIT_USER": {"HEADER": "Manage User", "EDIT_EXISTING_USER": "Edit Existing User", "MAIN": "Основни", "USER_ORGANIZATIONS": "Организации"}, "REMOVE_USER": "'{{ name }}' was removed", "ACTIVE": "Акти<PERSON><PERSON>н", "NOT_STARTED": "Not Started"}, "CONTEXT_MENU": {"TIMER": "Страр<PERSON>и<PERSON><PERSON><PERSON> таймер", "ADD_INCOME": "Доход", "ADD_EXPENSE": "Разходи", "INVOICE": "Фактура", "ESTIMATE": "Estimate", "PAYMENT": "Payment", "TIME_LOG": "Time Log", "CANDIDATE": "Candidate", "PROPOSAL": "Предложение", "CONTRACT": "Договор", "TEAM": "Team", "TASK": "Задача", "CLIENT": "Кли<PERSON><PERSON>т", "CONTACT": "Contact", "PROJECT": "Проект", "ADD_EMPLOYEE": "Служител", "CHAT": "Чат за поддръжка", "FAQ": "FAQ", "HELP": "Помощ"}, "PROFILE_PAGE": {"FIRST_NAME": "Име", "LAST_NAME": "Фамилия", "EMAIL": "Eлектронна поща", "PASSWORD": "Парола", "REPEAT_PASSWORD": "Repeat Password", "ERROR": "Error", "SAVE": "Запази", "VALIDATION": {"EMAIL_REQUIRED": "Email is required!", "PASSWORDS_DO_NOT_MATCH": "Password Do Not Match!"}}, "INVITE_PAGE": {"USER": {"MANAGE": "Manage User Invites", "HEADER": "Invite Users", "ACTION": "Invite Users"}, "EMPLOYEE": {"MANAGE": "Manage Employee Invites", "HEADER": "Invite Employees", "ACTION": "Invite Employees"}, "CANDIDATE": {"MANAGE": "Manage Candidate In<PERSON>tes", "HEADER": "In<PERSON><PERSON>", "ACTION": "In<PERSON><PERSON>"}, "SENT": "покани изпратени.", "IGNORED": "общо покани изпратени. бяха вече поканени и бяха игнорирани.", "STATUS": {"INVITED": "Invited", "EXPIRED": "Expired", "ACCEPTED": "Прието"}, "INVITATION_EXPIRATION_OPTIONS": {"DAY": "1 Day", "WEEK": "7 Days", "TWO_WEEK": "14 Days", "MONTH": "30 Days", "NEVER": "Never"}}, "INVOICES_PAGE": {"SENDER": "Sender", "BROWSE": "Browse", "COMMENT": "Comment", "COMMENTS": "Comments", "HEADER": "Invoices", "INVOICE_NUMBER": "Invoice Number", "ESTIMATE_NUMBER": "Estimate Number", "INVOICE_DATE": "Invoice Date", "ESTIMATE_DATE": "Estimate Date", "DUE_DATE": "Due Date", "CURRENCY": "Валута", "DISCOUNT": "Discount", "CONTACT": "Contact", "TOTAL_VALUE": "Total Value", "PAID_STATUS": "Paid Status", "TAX": "Tax", "TAX_2": "Tax 2", "INVOICE_ACCEPTED": "Invoice Accepted", "INVOICE_REJECTED": "Invoice Rejected", "INVOICES_ADD_INVOICE": "Invoice Added", "INVOICES_ADD_ESTIMATE": "Estimated Added", "INVOICES_EDIT_INVOICE": "Invoice Edited", "INVOICES_EDIT_ESTIMATE": "Estimate Edited", "INVOICES_DUPLICATE_INVOICE": "Invoice Duplicated", "INVOICES_DUPLICATE_ESTIMATE": "Estimate Duplicated", "INVOICES_DELETE_INVOICE": "Invoice Deleted", "INVOICES_DELETE_ESTIMATE": "Estimate Deleted", "INVOICES_SELECT_INVOICE_DATE": "Invoice Date", "INVOICES_SELECT_DUE_DATE": "Due Date", "INVOICES_SELECT_CURRENCY": "Валута", "INVOICES_SELECT_DISCOUNT_VALUE": "Discount Value", "INVOICES_SELECT_DISCOUNT": "Discount", "INVOICES_SELECT_PAID": "Paid", "INVOICES_TAXES": "Taxes", "INVOICES_SELECT_TERMS": "Terms", "ADD_INVOICE": "Add Invoice", "ADD_ESTIMATE": "Add Estimate", "EDIT_INVOICE": "Edit Invoice", "EDIT_ESTIMATE": "Edit Estimate", "VIEW_INVOICE": "View Invoice", "VIEW_ESTIMATE": "View Estimate", "SELECT_EMPLOYEE": "Select Employee", "SELECT_PROJECT": "Select Project", "SELECT_TASK": "Select Task", "SELECT_PRODUCT": "Select Product", "INVALID_DATES": "Invalid Dates", "INVOICE_NUMBER_DUPLICATE": "Invoice Number already exists", "DISCOUNT_TYPE": "Discount Type", "TAX_TYPE": "Tax Type", "TAX_VALUE": "Tax Value", "PERCENT": "Percent", "FLAT": "Flat", "SUBTOTAL": "Subtotal", "TOTAL": "Total", "NAME": "Име", "PAID": "Paid", "NOT_PAID": "Not Paid", "RECEIVED_INVOICES": "Received Invoices", "RECEIVED_ESTIMATES": "Received Estimates", "SEND_INVOICE": "Invoice Sent", "SEND_ESTIMATE": "Estimate Sent", "APPLY_TAX": "Apply Tax", "APPLY_DISCOUNT": "Apply Discount", "APPLIED": "Applied", "NOT_APPLIED": "Not Applied", "STATUS": "Статус", "SET_STATUS": "SET STATUS", "SHOW_COLUMNS": "Show Columns", "ITEM": "<PERSON><PERSON>", "SETTINGS": "Настройки", "SHOW_HIDE_COLUMNS": "Show/Hide Columns", "INVOICES_PER_PAGE": "Invoices Per Page", "ESTIMATES_PER_PAGE": "Estimates Per Page", "APPLY_DISCOUNT_AFTER_TAX": "Apply discount after tax", "SELECT_INVOICE_TO_VIEW_HISTORY": "Select an invoice to view its history.", "INVOICE_SENT_TO": "Invoice sent to '{{ name }}'", "ESTIMATE_SENT_TO": "Estimate sent to '{{ name }}'", "INVOICE": "Фактура", "ESTIMATE": "Estimate", "ACTIONS": "Actions", "HISTORY": "History", "SEARCH": "Search", "NUMBER": "Number", "FROM": "FROM", "TO": "TO", "DATE": "Дата", "YES": "Yes", "NO": "No", "ALREADY_PAID": "Already Paid", "AMOUNT_DUE": "Amount Due", "INVOICED_REMAINING_AMOUNT": "Invoiced Remaining Amount", "INVOICE_TYPE": {"INVOICE_TYPE": "Invoice Type", "ESTIMATE_TYPE": "Estimate Type", "GENERATE_INVOICE_ITEMS": "Generate Invoice Items", "GENERATE_ESTIMATE_ITEMS": "Generate Estimate Items", "GENERATE_FOR_UNINVOICED_EXPENSES": "Generate for all uninvoiced expenses", "BY_EMPLOYEE_HOURS": "By Employee Hours", "BY_PROJECT_HOURS": "By Project Hours", "BY_TASK_HOURS": "By Task Hours", "BY_PRODUCTS": "By Products", "BY_EXPENSES": "By Expenses", "DETAILED_ITEMS": "Detailed Items", "SELECT_INVOICE_TYPE": "Select an invoice type", "SELECT_ESTIMATE_TYPE": "Select an estimate type", "SELECT_PROJECTS": "Select projects", "SELECT_TASKS": "Select tasks", "SELECT_PRODUCTS": "Select products", "SELECT_EXPENSES": "Select expenses"}, "INVOICE_ITEM": {"ITEM_NUMBER": "Item Number", "TASK": "Задача", "NAME": "Име", "DESCRIPTION": "Description", "PRICE": "Price", "QUANTITY": "Quantity", "TOTAL_VALUE": "Total Value", "EMPLOYEE": "Служител", "HOURLY_RATE": "Hourly Rate", "HOURS_WORKED": "Hours Worked", "PROJECT": "Проект", "PRODUCT": "Product", "EXPENSE": "Разходи", "NO_ITEMS": "Please add invoice items", "INVALID_ITEM": "Invalid Invoice Item", "EMPLOYEE_VALUE": "Employee cannot be empty", "PROJECT_VALUE": "Project cannot be empty", "TASK_VALUE": "Task cannot be empty", "ITEM": "<PERSON><PERSON>"}, "SEND": {"CONFIRMATION_INVOICE": "Send this invoice to", "CONFIRMATION_ESTIMATE": "Send this estimate to", "ALREADY_SENT_INVOICE": "This invoice has already been sent to", "ALREADY_SENT_ESTIMATE": "This estimate has already been sent to", "NOT_LINKED": "Client does not have an organization", "SENT": "Изпратено", "NOT_SENT": "Not Sent"}, "VIEW": {"FROM": "FROM", "TO": "TO"}, "DOWNLOAD": {"CONFIRMATION_INVOICE": "Download this invoice ?", "CONFIRMATION_ESTIMATE": "Download this estimate ?", "INVOICE_DOWNLOAD": "Invoice downloaded", "ESTIMATE_DOWNLOAD": "Estimate downloaded"}, "EMAIL": {"EMAIL_INVOICE": "Send this invoice by email ?", "EMAIL_ESTIMATE": "Send this estimate by email ?", "EMAIL_SENT": "<PERSON><PERSON>"}, "ESTIMATES": {"HEADER": "Estimates", "ESTIMATE_NUMBER": "Estimate Number", "ESTIMATE_DATE": "Estimate Date", "ACCEPT": "Accept", "REJECT": "Reject", "ACCEPTED": "Прието", "REJECTED": "Rejected", "ACCEPTED_STATUS": "Accepted Status", "ESTIMATE_CONVERT": "Estimate Converted", "SELECT_ESTIMATE_TO_VIEW_HISTORY": "Select an estimate to view its history.", "ESTIMATE_ACCEPTED": "Estimate accepted", "ESTIMATE_REJECTED": "Estimate rejected", "ERROR": "An error occurred", "CONVERTED_TO_INVOICE": "Estimate converted to invoice"}, "PAYMENTS": {"HEADER": "Payments for Invoice", "TOTAL_VALUE": "Total Invoice Value", "RECORD_PAYMENT": "Record Payment", "EDIT_PAYMENT": "Edit Payment", "DELETE_PAYMENT": "Delete Payment", "TOTAL_PAID": "Total Paid", "PAID": "Paid", "PAYMENT_DATE": "Payment Date", "AMOUNT": "Сума", "RECORDED_BY": "Recorded By", "NOTE": "Note", "PAYMENT_ADD": "Payment was added", "PAYMENT_EDIT": "Payment was changed", "PAYMENT_DELETE": "Payment was removed", "PAYMENT_DOWNLOAD": "Payment Downloaded", "STATUS": "Статус", "ON_TIME": "On time", "OVERDUE": "Overdue", "NO_PAYMENTS_RECORDED": "No payments recorded", "LEFT_TO_PAY": "Left to pay", "PAYMENT_METHOD": "Payment Method", "SELECT_INVOICE": "Select Invoice", "PAYMENT_AMOUNT_ADDED": "Payment of {{ amount }} {{ currency }} added", "PAYMENT": "Payment", "BANK_TRANSFER": "Bank Transfer", "CASH": "Cash", "CHEQUE": "Cheque", "CREDIT_CARD": "Credit Card", "DEBIT": "Debit", "ONLINE": "Online", "PAYMENTS_FOR_INVOICE": "Payments for invoice", "RECEIVED_FROM": "Received from", "RECEIVER": "Receiver", "SEND_RECEIPT": "Send this receipt to {{ name }} ?", "CONTACT_GREETING": "Hi {{ name }},", "RECEIPT_FOR": "This is your receipt for Invoice {{ invoiceNumber }} for {{ amount }} {{ currency }}.", "BEST_REGARDS": "Best regards the {{ name }} team."}, "INTERNAL_NOTE": {"NOTE_SAVED": "Internal Note was added", "ADD_INTERNAL_NOTE": "Add Internal Note", "ADD_NOTE": "Add note", "NOTE": "Note", "INTERNAL_NOTE": "Internal Note"}, "STATUSES": {"DRAFT": "Draft", "SENT": "Изпратено", "VIEWED": "Viewed", "FULLY_PAID": "<PERSON>y Paid", "PARTIALLY_PAID": "Partially Paid", "OVERPAID": "Overpaid", "VOID": "Void", "ACCEPTED": "Прието", "REJECTED": "Rejected"}, "ACTION": {"DUPLICATE": "Дублиране", "SEND": "Send", "CONVERT_TO_INVOICE": "Convert to invoice", "EMAIL": "Eлектронна поща", "DELETE": "Изтрий", "NOTE": "Note", "PAYMENTS": "Payments"}, "PUBLIC_LINK": {"HEADER": "Generate Public Link", "GENERATE": "Generate a link to the {{ text }} that anyone with the link can view.", "ACCESS": "Anyone with access to this link can view the {{ text }}."}}, "PAYMENTS_PAGE": {"HEADER": "Payments", "CONTACT": "Contact", "AMOUNT": "Сума", "PAYMENT_DATE": "Payment Date", "RECORDED_BY": "Recorded By", "NOTE": "Note", "STATUS": "Статус", "ON_TIME": "On time", "OVERDUE": "Overdue", "PAYMENT_METHOD": "Payment Method", "PROJECT": "Проект", "TAGS": "Тагове"}, "HEADER": {"SELECT_EMPLOYEE": "Select Employee", "SELECT_A_DATE": "Select A date", "SELECT_AN_ORGANIZATION": "Select An Organization", "SELECT_PROJECT": "Select Project", "SELECT_TEAM": "Select Team"}, "HEADER_TITLE": {"FOR": "for", "FROM": "from"}, "PAGE_NOT_FOUND": {"404_PAGE_NOT_FOUND": "404 Page Not Found", "TAKE_ME_HOME": "Take me home", "THE_PAGE_YOU_WERE_LOOKING_FOR_DOESNT_EXIST": "The page you were looking for doesn't exist"}, "HELP_PAGE": {"HELP": "Помощ", "KNOWLEDGE_BASE": "Knowledge base", "CHOSE_ICON": "Chose icon to set", "CREATED_AT": "created at", "WRITTEN_BY": "written by", "EMPLOYEES": "служители", "ONLY_FOR_EMPLOYEES": "Only for employees", "DRAFT": "draft", "ADD_ARTICLE": "Add article", "CHOOSE_ANY_CATEGORY": "Choose any category", "ARTICLES": "articles", "REMOVE_ARTICLE": "Remove Article", "ARE_YOU_SURE": "Are you sure? This cannot be undone.", "DESCRIPTION": "Description", "ARTICLE_TEXT": "Article text", "EDIT_ARTICLE": "Edit Article", "MANAGE_CATEGORY": "Manage Category", "ADD_CATEGORY": "Add Category", "EDIT_BASE": "Edit Knowledge Base", "DELETE_BASE": "Delete Base", "LANGUAGE": "<PERSON>зи<PERSON> (Language)", "PUBLISH_STATUS": "Publish Status", "PRIVATE_STATUS": "Private Status", "COLOR": "Color", "NAME_CATEGORY": "Name of the category", "NAME_ARTICLE": "Name of the Article", "ADD_BASE": "Add Knowledge Base", "MANAGE_BASE": "Manage Knowledge Base", "NAME_OF_THE_BASE": "Name of the base", "REMOVE_CATEGORY": "Remove Category", "REMOVE_BASE": "Remove Base", "CLEAR": "Clear", "SEARCH_BY_NAME": "Search by name", "FILTER_BY_AUTHOR": "Filter by author", "CATEGORY_EDIT_ADDED": "Category was added", "CATEGORY_EDIT_UPDATED": "Category was changed", "CATEGORY_EDIT_DELETED": "Category was removed"}, "PROJECT_MANAGEMENT_PAGE": {"THIS_TAB_WILL_SHOW_PROJECT_MANAGEMENT_CHARTS_AND_AGGREGATED_DATA": "This tab will show project management charts and aggregated data."}, "SETTINGS_FEATURES": {"INVOICE": "Фактура", "INCOME": "Доход", "EXPENSE": "Разходи", "PAYMENT": "Payment", "PROPOSAL": "Предложение", "SALES_PIPELINE": "Sales Pipeline", "TASK_DASHBOARD": " Task Dashboard", "JOBS": "Jobs", "EMPLOYEES": "Служители", "TIME_ACTIVITY": "Time Activity", "TIMESHEET": "Timesheet", "APPOINTMENT_SCHEDULE": "Appointment & Schedule", "CANDIDATE": "Candidate", "MANAGE_ORGANIZATION": "Manage Organization", "PRODUCT_INVENTORY": "Product Inventory", "PROJECT": "Проект", "ORGANIZATION_TEAM": "Organization Team", "ORGANIZATION_DOCUMENT": "Organization Document", "LEAD_CUSTOMER_CLIENT": "Lead, Customer & Client", "GOAL_AND_OBJECTIVE": "Goal and Objective", "ALL_REPORT": "All Report", "USERS": "Users", "ORGANIZATIONS": "Организации", "APPS_INTEGRATIONS": "Apps & Integrations", "EMAIL_HISTORY": "Email History", "SETTING": "Setting", "ENTITY_IMPORT_EXPORT": "Entity Import & Export", "CUSTOM_SMTP": "Custom SMTP", "ROLES_PERMISSIONS": "Роли и Позволения", "TIME_TRACKING": "Time Tracking", "ESTIMATE": "Estimate", "DASHBOARD": "Табло"}, "SETTINGS_FEATURES_DESCRIPTION": {"INVOICE": {"MANAGE_INVOICE_CREATE_FIRST_INVOICE": "Manage Invoice, Create First Invoice"}, "INCOME": {"CREATE_FIRST_INCOME": "Create First Income"}, "EXPENSE": {"CREATE_FIRST_EXPENSE": "Create First Expense"}, "PAYMENT": {"MANAGE_PAYMENT_CREATE_FIRST_PAYMENT": "Manage Payment, Create First Payment"}, "PROPOSAL": {"MANAGE_PROPOSAL_REGISTER_FIRST_PROPOSAL": "Manage Proposal, Register First Proposal"}, "SALES_PIPELINE": {"CREATE_SALES_PIPELINE": "Create Sales Pipeline"}, "TASK_DASHBOARD": {"TASK_DASHBOARD": "Task Dashboard"}, "JOBS": {"JOB_SEARCH_JOBS_MATCHING": "Job Search & Jobs Matching"}, "EMPLOYEES": {"MANAGE_EMPLOYEES_ADD_OR_INVITE_EMPLOYEES": "Manage Employees, Add or Invite Employees"}, "TIME_ACTIVITY": {"MANAGE_TIME_ACTIVITY_SCREENSHOTS_APP_VISITED_SITES_ACTIVITIES": "Manage Time Activity, Screenshots, App, Visited Sites, Activities"}, "TIMESHEET": {"MANAGE_EMPLOYEE_TIMESHEET_DAILY_WEEKLY_CALENDAR_CREATE_FIRST_TIMESHEET": "Manage Employee Timesheet Daily, Weekly, Calendar, Create First Timesheet"}, "APPOINTMENT_SCHEDULE": {"EMPLOYEE_APPOINTMENT_SCHEDULES_BOOK_PUBLIC_APPOINTMENT": "Employee Appointment, Schedules & Book Public Appointment"}, "CANDIDATE": {"MANAGE_CANDIDATES_INTERVIEWS_INVITES": "Manage Candidates, Interviews & Invites"}, "MANAGE_ORGANIZATION": {"MANAGE_ORGANIZATION_DETAILS_LOCATION_AND_SETTINGS": "Manage Organization Details, Location and Settings"}, "PRODUCT_INVENTORY": {"MANAGE_PRODUCT_INVENTORY_CREATE_FIRST_PRODUCT": "Manage Product Inventory, Create First Product"}, "PROJECT": {"MANAGE_PROJECT_CREATE_FIRST_PROJECT": "Manage Project, Create First Project"}, "ORGANIZATION_TEAM": {"MANAGE_ORGANIZATION_TEAM_CREATE_FIRST_TEAM": "Manage Organization Team, Create First Team"}, "ORGANIZATION_DOCUMENT": {"MANAGE_ORGANIZATION_DOCUMENT_CREATE_FIRST_DOCUMENT": "Manage Organization Document, Create First Document"}, "LEAD_CUSTOMER_CLIENT": {"MANAGE_LEADS_CUSTOMERS_AND_CLIENTS_CREATE_FIRST_CUSTOMER/CLIENTS": "Manage Leads, Customers and Clients, Create First Customer/Clients"}, "GOAL_AND_OBJECTIVE": {"MANAGE_GOALS_AND_OBJECTIVES": "Manage Goals and Objectives"}, "ALL_REPORT": {"MANAGE_EXPENSE_WEEKLY_TIME_ACTIVITY_AND_ETC_REPORTS": "Manage Expense, Weekly, Time & Activity and etc reports"}, "USERS": {"MANAGE_TENANT_USERS": "Manage Tenant Users"}, "ORGANIZATIONS": {"MANAGE_TENANT_ORGANIZATIONS": "Manage Tenant Organizations"}, "APPS_INTEGRATIONS": {"MANAGE_AVAILABLE_APPS_INTEGRATIONS_LIKE_UPWORK_HUBSTAFF": "Manage Available Apps & Integrations Like Upwork & Hubstaff"}, "EMAIL_HISTORY": {"MANAGE_EMAIL_HISTORY": "Manage Email History"}, "SETTING": {"MANAGE_SETTING": "Manage Setting"}, "ENTITY_IMPORT_EXPORT": {"MANAGE_ENTITY_IMPORT_AND_EXPORT": "Manage Entity Import and Export"}, "CUSTOM_SMTP": {"MANAGE_TENANT_ORGANIZATION_CUSTOM_SMTP": "Manage Tenant & Organization Custom SMTP"}, "ROLES_PERMISSIONS": {"MANAGE_ROLES_PERMISSIONS": "Manage Roles & Permissions"}, "TIME_TRACKING": {"DOWNLOAD_DESKTOP_APP_CREATE_FIRST_TIMESHEET": "Download Desktop App, Create First Timesheet"}, "ESTIMATE": {"MANAGE_ESTIMATE_CREATE_FIRST_ESTIMATE": "Manage Estimate, Create First Estimate"}, "DASHBOARD": {"GO_TO_DASHBOARD_MANAGE_EMPLOYEE_STATISTICS_TIME_TRACKING_DASHBOARD": "Go to dashboard, Manage Employee Statistics, Time Tracking Dashboard"}}, "SETTINGS_FEATURES_TEXT": {"INVOICE": {"INVOICE_RECEIVED": "Invoice Received"}, "INCOME": {"": ""}, "EXPENSE": {"EMPLOYEE_RECURRING_EXPENSE": "Разходи, повтарящи се за служителя", "ORGANIZATION_RECURRING_EXPENSES": "Organization Recurring Expenses"}, "PAYMENT": {"": ""}, "PROPOSAL": {"PROPOSAL_TEMPLATE": "Proposal Template"}, "SALES_PIPELINE": {"SALES_PIPELINE_DEAL": "Sales Pipeline Deal"}, "TASK_DASHBOARD": {"TEAM_TASK_DASHBOARD": "Team Task Dashboard", "MY_TASK_DASHBOARD": "My Task Dashboard"}, "JOBS": {"": ""}, "EMPLOYEES": {"EMPLOYEE_LEVEL": "Employee Level", "EMPLOYEE_POSITION": "Employee Position", "EMPLOYEE_TIME_OFF": "Employee Time Off", "EMPLOYEE_APPROVAL": "Employee Approval", "EMPLOYEE_APPROVAL_POLICY": "Employee Approval Policy"}, "TIME_ACTIVITY": {"": ""}, "TIMESHEET": {"": ""}, "APPOINTMENT_SCHEDULE": {"": ""}, "CANDIDATE": {"MANAGE_INTERVIEW": "Manage Interview", "MANAGE_INVITE": "Manage Invite"}, "MANAGE_ORGANIZATION": {"HELP_CENTER": "Help Center", "ORGANIZATION_TAG": "Organization Tag", "ORGANIZATION_EQUIPMENT": "Organization Equipment", "ORGANIZATION_VENDOR": "Organization Vendor", "ORGANIZATION_DEPARTMENT": "Organization Department", "ORGANIZATION_EMPLOYMENT_TYPE": "Organization Employment Type"}, "PRODUCT_INVENTORY": {"": ""}, "PROJECT": {"": ""}, "ORGANIZATION_TEAM": {"": ""}, "ORGANIZATION_DOCUMENT": {"": ""}, "LEAD_CUSTOMER_CLIENT": {"": ""}, "GOAL_AND_OBJECTIVE": {"GOAL_TIME_FRAME_KPI": "Goal Time Frame & KPI"}, "ALL_REPORT": {"": ""}, "USERS": {"": ""}, "ORGANIZATIONS": {"": ""}, "APPS_INTEGRATIONS": {"": ""}, "EMAIL_HISTORY": {"CUSTOM_EMAIL_TEMPLATE": "Custom Email Template"}, "SETTING": {"FILE_STORAGE": "File Storage", "SMS_GATEWAY": "SMS Gateway"}, "ENTITY_IMPORT_EXPORT": {"": ""}, "CUSTOM_SMTP": {"": ""}, "ROLES_PERMISSIONS": {"": ""}, "TIME_TRACKING": {"": ""}, "ESTIMATE": {"ESTIMATE_RECEIVED": "Estimate Received"}, "DASHBOARD": {"": ""}}, "ABOUT_PAGE": {"ABOUT": "За нас"}, "FOOTER": {"BY": "от", "RIGHTS_RESERVED": "Всички права запазени.", "PRESENT": "Present", "TERMS_OF_SERVICE": "Terms Of Service", "PRIVACY_POLICY": "Privacy Policy"}, "TOASTR": {"TITLE": {"SUCCESS": "Success", "ERROR": "Error", "INFO": "Info", "WARNING": "Warning", "MAX_LIMIT_REACHED": "Max limit reached"}, "MESSAGE": {"ERRORS": "Please check the form for errors", "PROJECT_LOAD": "Could Not Load Projects", "COPIED": "Link copied to clipboard", "INVITES_LOAD": "Could Not Load Invites", "INVITES_RESEND": "In<PERSON><PERSON> has been resent to '{{ email }}'.", "INVITES_DELETE": "'{{ email }}' was removed", "EMPLOYEE_DEPARTMENT_ADDED": "Employee added to department", "EMPLOYEE_DEPARTMENT_REMOVED": "Employee removed from department", "EMPLOYEE_PROJECT_ADDED": "Employee added to project", "EMPLOYEE_PROJECT_REMOVED": "Employee removed from project", "EMPLOYEE_CLIENT_ADDED": "Employee added to the client", "EMPLOYEE_CLIENT_REMOVED": "Employee removed from the client", "EMPLOYEE_EDIT_ERROR": "Error in editing employee", "EMPLOYEE_PROFILE_UPDATE": "Profile '{{name}}' was changed", "EMPLOYEE_LEVEL_UPDATE": "Employee Level '{{ name }}' was changed", "EMPLOYEE_ADDED": "Employee '{{name}}' added to '{{organization}}'", "EMPLOYEE_INACTIVE": "Employee '{{name}}' set as inactive.", "EMPLOYEE_ACTIVE": "Employee '{{name}}' set as active.", "EMPLOYEE_JOB_STATUS_ACTIVE": "Employee '{{name}}' job status set as active.", "EMPLOYEE_JOB_STATUS_INACTIVE": "Employee '{{name}}' job status set as inactive.", "EMPLOYEE_TIME_TRACKING_ENABLED": "Enabled time tracking for '{{name}}'.", "EMPLOYEE_TIME_TRACKING_DISABLED": "Disabled time tracking for '{{name}}'.", "CONFIRM": "Потвърждение", "ARE_YOU_SURE_YOU_WANT_TO_RESEND_THE_INVITE_TO": "Are you sure you want to resend the invite to", "OK": "OK", "CANCEL": "Отказ", "ARE_YOU_SURE_YOU_WANT_TO_CHANGE_THE": "Are you sure you want to change the", "PERMISSION_UPDATED": "разрешения обновени за", "ROLE_CREATED": "{{ name }} successfully created", "ROLE_DELETED": "{{ name }} successfully deleted", "ROLE_CREATED_ERROR": "Error while creating {{ name }} role", "ROLE_DELETED_ERROR": "Error while deleting {{ name }} role", "PERMISSION_UPDATE_ERROR": "There was an error in updating the permissions, please refresh & try again", "REGISTER_PROPOSAL_NO_EMPLOYEE_MSG": "Employee is required!", "NEW_ORGANIZATION_PROJECT_INVALID_NAME": "Invalid input", "NEW_ORGANIZATION_TEAM_INVALID_NAME": "Team name and members are required", "NEW_ORGANIZATION_VENDOR_INVALID_NAME": "Vendor name is required", "NEW_ORGANIZATION_EXPENSE_CATEGORY_INVALID_NAME": "Expense category name is required", "NEW_ORGANIZATION_POSITION_INVALID_NAME": "Position name is required", "NEW_ORGANIZATION_EMPLOYEE_LEVEL_INVALID_NAME": "Employee Level is required", "NEW_ORGANIZATION_INVALID_EMPLOYMENT_TYPE": "Employment type name is required", "NEW_ORGANIZATION_DEPARTMENT_INVALID_NAME": "Department name is required", "NEW_ORGANIZATION_CLIENT_INVALID_DATA": "Доход добавен за", "NEW_ORGANIZATION_AWARD_INVALID_NAME": "Vendor name and year are required", "NEW_ORGANIZATION_LANGUAGE_INVALID_NAME": "Language name and level are required", "MAIN_ORGANIZATION_UPDATED": "'{{ name }}' info was changed", "CANDIDATE_SKILL_REQUIRED": "Skill name is required", "CANDIDATE_EDIT_CREATED": "Successfully Created", "CANDIDATE_EDIT_UPDATED": "Successfully updated", "CANDIDATE_EDIT_DELETED": "Successfully deleted", "CANDIDATE_EDUCATION_REQUIRED": "Education information is required", "CANDIDATE_FEEDBACK_REQUIRED": "Feedback information is required", "CANDIDATE_FEEDBACK_ABILITY": "All interviewers have already left feedback", "CANDIDATE_EXPERIENCE_REQUIRED": "Experience information is required", "CANDIDATE_DOCUMENT_REQUIRED": "Document information is required", "CANDIDATE_SKILLS_REQUIRED": "<PERSON><PERSON>'s name is required", "CANDIDATE_PROFILE_UPDATE": "Profile '{{name}}' was changed.", "NAME_REQUIRED": "Name is required!", "CONTACT_TYPE_REQUIRED": "Contact Type is required!", "EMAIL_REQUIRED": "Email is required!", "EMAIL_SHOULD_BE_REAL": "Email should be a real one!", "PHONE_REQUIRED": "Phone is required!", "PHONE_CONTAINS_ONLY_NUMBERS": "Phone should only contain numbers!", "SOMETHING_BAD_HAPPENED": "Something bad happened!", "EMAIL_TEMPLATE_SAVED": "Email template '{{ templateName }}' was saved", "DELETED": "Successfully deleted", "CREATED": "Successfully created", "UPDATED": "Successfully updated", "MOVED_BASE": "Base was moved", "MOVED_CATEGORY": "Category was moved", "CREATED_BASE": "Base '{{ name }}' was added", "EDITED_BASE": "Base '{{ name }}' was changed", "DELETED_BASE": "Base '{{ name }}' was removed", "DELETED_CATEGORY": "Category '{{ name }}' was removed", "CREATED_CATEGORY": "Category was added", "EDIT_ADD_CATEGORY": "Category '{{ name }}' was added", "EDITED_CATEGORY": "Category '{{ name }}' was changed", "HELP_ARTICLE_CREATED": "Article was added", "HELP_ARTICLE_UPDATED": "Article '{{ name }}' was changed", "HELP_ARTICLE_DELETED": "Article '{{ name }}' was removed", "PIPELINE_CREATED": "Pipeline '{{ name }}' was added", "PIPELINE_UPDATED": "Pipeline '{{ name }}' was changed", "PIPELINE_DELETED": "Pipeline '{{ name }}' was removed", "OBJECTIVE_ADDED": "Objective was added", "OBJECTIVE_DELETED": "Objective was removed", "OBJECTIVE_UPDATED": "Objective was changed", "KEY_RESULT_ADDED": "Key Result was added", "KEY_RESULT_DELETED": "Key Result was removed", "KEY_RESULT_UPDATED": "Key Result was changed", "TIME_FRAME_CREATED": "Time frame '{{ name }}' was added", "TIME_FRAME_UPDATED": "Time frame '{{ name }}' was changed", "TIME_FRAME_DELETED": "Time frame '{{ name }}' was removed", "KPI_CREATED": "KPI was added", "KPI_UPDATED": "KPI was changed", "KPI_DELETED": "KPI was removed", "EDIT_PAST_INTERVIEW": "Editing past interviews is prohibited", "ARCHIVE_INTERVIEW": "This interview has already been archived", "DELETE_PAST_INTERVIEW": "Deleting past interviews is prohibited", "GOAL_GENERAL_SETTING_UPDATED": "Goal General settings updated", "MAX_OBJECTIVE_LIMIT": "You cannot create any more Objectives. Please change maximum objective limit to add more objectives.", "MAX_KEY_RESULT_LIMIT": "You cannot create any more Key Results for this Objective. Please change maximum key result limit to add more Key Results.", "CUSTOM_SMTP_ADDED": "Smtp settings successfully created", "CUSTOM_SMTP_UPDATED": "Smtp settings successfully updated", "JOB_MATCHING_SAVED": "Criterion was changed", "JOB_MATCHING_ERROR": "Error while saving criterion, Please try aging", "JOB_MATCHING_DELETED": "Criterion was removed", "APPROVAL_POLICY_CREATED": "Approval policy '{{ name }}' was added", "APPROVAL_POLICY_UPDATED": "Approval policy '{{ name }}' was changed", "APPROVAL_POLICY_DELETED": "Approval policy '{{ name }}' was removed", "CANDIDATE_CREATED": "Candidate '{{ name }}' was added to '{{ organization }}'", "CANDIDATE_ARCHIVED": "Candidate '{{ name }}' set as archived.", "CANDIDATE_REJECTED": "Candidate '{{ name }}' set as rejected.", "CANDIDATE_HIRED": "Candidate '{{ name }}' set as hired.", "CANDIDATE_DELETED": "Candidate '{{ name }}' was removed", "PRESET_SAVED": "Preset successfully saved", "JOB_APPLIED": "Job applied successfully", "JOB_HIDDEN": "Job hidden successfully", "ORGANIZATION_LOCATION_UPDATED": "'{{ name }}' organization location was changed", "ORGANIZATION_INFO_UPDATED": "'{{ name }}' organization main info was changed", "ORGANIZATION_SETTINGS_UPDATED": "'{{ name }}' organization settings was changed", "ORGANIZATION_TASK_SETTINGS_UPDATE_ERROR": "Възникна грешка при опит за актуализиране на настройката на задачата на организацията.", "SETTINGS_SAVED": "Setting<PERSON> saved successfully", "KEY_RESULTS_CREATED": "Key Results Created", "INVITE_EMAIL_DELETED": "{{ name }} has been deleted.", "HOLIDAY_ERROR": "Unable to get holidays", "INTERVAL_ERROR": "Please pick correct dates and try again", "PROFILE_UPDATED": "Your profile was changed", "PERSONAL_QUALITIES_CREATED": "Personal Qualities '{{ name }}' was added", "PERSONAL_QUALITIES_UPDATED": "Personal Qualities '{{ name }}' was changed", "PERSONAL_QUALITIES_DELETED": "Personal Qualities '{{ name }}' was removed", "TECHNOLOGY_STACK_CREATED": "Technology Stack '{{ name }}' was added", "TECHNOLOGY_STACK_UPDATED": "Technology Stack '{{ name }}' was changed", "TECHNOLOGY_STACK_DELETED": "Technology Stack '{{ name }}' was removed", "ARCHIVE_INTERVIEW_SET": "'{{ name }}' set as archived.", "INTERVIEW_UPDATED": "'{{ name }}' was changed", "INTERVIEW_DELETED": "'{{ name }}' was removed", "INTERVIEW_FEEDBACK_CREATED": "Feedback was added for '{{ name }}'.", "CANDIDATE_EDUCATION_CREATED": "Education '{{ name }}' was added", "CANDIDATE_EDUCATION_UPDATED": "Education '{{ name }}' was changed", "CANDIDATE_EDUCATION_DELETED": "Education '{{ name }}' was removed", "CANDIDATE_EXPERIENCE_CREATED": "Experience '{{ name }}' was added", "CANDIDATE_EXPERIENCE_UPDATED": "Experience '{{ name }}' was changed", "CANDIDATE_EXPERIENCE_DELETED": "Experience '{{ name }}' was removed", "CANDIDATE_SKILL_CREATED": "Skill '{{ name }}' was added", "CANDIDATE_SKILL_UPDATED": "Skill '{{ name }}' was changed", "CANDIDATE_SKILL_DELETED": "Skill '{{ name }}' was removed", "CANDIDATE_DOCUMENT_CREATED": "Document '{{ name }}' was added", "CANDIDATE_DOCUMENT_UPDATED": "Document '{{ name }}' was changed", "CANDIDATE_DOCUMENT_DELETED": "Document '{{ name }}' was removed", "CANDIDATE_INTERVIEW_CREATED": "Interview '{{ name }}' was added", "CANDIDATE_INTERVIEW_UPDATED": "Interview '{{ name }}' was changed", "CANDIDATE_INTERVIEW_DELETED": "Interview '{{ name }}' was removed", "CANDIDATE_FEEDBACK_CREATED": "Feedback was added", "CANDIDATE_FEEDBACK_UPDATED": "Feedback was changed", "CANDIDATE_FEEDBACK_DELETED": "Feedback was removed", "RECURRING_EXPENSE_SET": "Recurring expense set for '{{ name }}'", "RECURRING_EXPENSE_UPDATED": "Recurring expense was changed for '{{ name }}'", "RECURRING_EXPENSE_DELETED": "Recurring expense was removed for '{{ name }}'", "IMAGE_UPDATED": "The image has been updated", "ORGANIZATION_PAGE_UPDATED": "Page was changed for '{{ name }}'", "SCREENSHOT_DELETED": "Screenshot for '{{ name }}' removed from '{{ organization }}'", "TIME_LOG_DELETED": "Time log for '{{ name }}' removed from '{{ organization }}'", "TIME_LOGS_DELETED": "Time logs removed from '{{ organization }}'", "BUCKET_CREATED": "'{{ bucket }}' for '{{ region }}' has been created successfully", "AUTHORIZED_TO_WORK": "{{ name }} is authorized to work"}}, "ACCEPT_INVITE": {"ACCEPT_INVITE_FORM": {"FULL_NAME": "Две имена", "ENTER_YOUR_FULL_NAME": "Enter Your Full Name", "PASSWORD": "Парола", "REPEAT_PASSWORD": "Repeat Password", "AGREE_TO": "Agree to", "TERMS_AND_CONDITIONS": "Terms & Conditions", "ADD_ORGANIZATION": "Add Organization", "COMPLETE_REGISTRATION": "Complete Registration", "PASSWORDS_DO_NOT_MATCH": "Password Do Not Match!"}, "INVALID_INVITE": "Either you entered an incorrect URL or the invitation has expired", "HEADING": "Accept Invitation to {{ organizationName }}", "SUB_HEADING": "Complete your registration {{ email }}", "INVITATION_NO_LONGER_VALID": "This invitation is no longer valid", "ACCOUNT_CREATED": "Your account has been created, please login", "COULD_NOT_CREATE_ACCOUNT": "Could not create your account"}, "NOTES": {"INCOME": {"ADD_INCOME": "Income was added for '{{ name }}'", "EDIT_INCOME": "Income was changed for '{{ name }}'", "DELETE_INCOME": "Income was removed for '{{ name }}'", "INCOME_ERROR": "{{ error }}"}, "INVOICE": {"ADD_INVOICE": "Invoice added for '{{ name }}'", "EDIT_INVOICE": "Invoice edited for '{{ name }}'", "DELETE_INVOICE": "Invoice deleted for '{{ name }}'", "INVOICE_ERROR": "{{ error }}"}, "EXPENSES": {"ADD_EXPENSE": "Expense added for '{{ name }}'", "OPEN_EDIT_EXPENSE_DIALOG": "Expense edited for '{{ name }}'", "DELETE_EXPENSE": "Expense deleted for '{{ name }}'", "EXPENSES_ERROR": "{{ error }}"}, "PROPOSALS": {"EDIT_PROPOSAL": "Proposal successfully updated", "REGISTER_PROPOSAL": "New proposal was added", "REGISTER_PROPOSAL_NO_EMPLOYEE_SELECTED": "Please select an employee from the dropdown menu.", "REGISTER_PROPOSAL_ERROR": "{{ error }}", "DELETE_PROPOSAL": "Proposal was removed", "PROPOSAL_ACCEPTED": "Proposal status updated to Accepted", "PROPOSAL_SENT": "Proposal status updated to Sen<PERSON>"}, "POLICY": {"ADD_POLICY": "Time off policy '{{ name }}' was added", "EDIT_POLICY": "Time off policy '{{ name }}' was changed", "DELETE_POLICY": "Time off policy '{{ name }}' was removed", "ERROR": "{{ error }}", "SAVE_ERROR": "Unable to create Policy record"}, "USER": {"EDIT_PROFILE": "Your profile has been updated successfully."}, "CANDIDATE": {"INVALID_FORM": "Please fill the form", "INVALID_FEEDBACK_INFO": "Please add feedback information", "EXPERIENCE": {"INVALID_CANDIDATE_NAME": "Please add a Skill name", "INVALID_FORM": "Please fill the form", "INVALID_FIELD": "Please fill the field", "ERROR": "{{ error }}"}}, "EMPLOYEE": {"EDIT_EMPLOYEE_AWARDS": {"ADD_AWARD": "New award '{{ name }}' was added", "INVALID_AWARD_NAME_YEAR": "Please check the Name and Year for Award input", "REMOVE_AWARD": "Award '{{ name }}' was removed"}}, "ORGANIZATIONS": {"ADD_NEW_ORGANIZATION": "Organization '{{ name }}' was added", "DELETE_ORGANIZATION": "Organization '{{ name }}' was removed", "ADD_NEW_USER_TO_ORGANIZATION": "'{{ username }}' was added to '{{ orgname }}'", "DELETE_USER_FROM_ORGANIZATION": "'{{ username }}' set as inactive.", "DATA_ERROR": "{{ error }}", "EDIT_ORGANIZATIONS_PROJECTS": {"ADD_PROJECT": "Project '{{ name }}' was saved", "REMOVE_PROJECT": "Project '{{ name }}' was removed", "INVALID_PROJECT_NAME": "Please fill in the name of your project", "VISIBILITY": "Now project is {{ name }} "}, "EDIT_ORGANIZATIONS_TEAM": {"ADD_NEW_TEAM": "Team '{{ name }}' was added", "EDIT_EXISTING_TEAM": "Team '{{ name }}' was changed", "INVALID_TEAM_NAME": "Please add a Team name and at least one member", "REMOVE_TEAM": "Team '{{ name }}' was removed"}, "EDIT_ORGANIZATIONS_EMPLOYEE_LEVELS": {"ADD_EMPLOYEE_LEVEL": "Employee Level '{{ name }}' was added", "REMOVE_EMPLOYEE_LEVEL": "Employee Level '{{ name }}' was removed", "INVALID_EMPLOYEE_LEVEL": "Please add an Employee Level"}, "EDIT_ORGANIZATIONS_VENDOR": {"ADD_VENDOR": "vendor '{{ name }}' was added", "UPDATE_VENDOR": "Vendor '{{ name }}' was changed", "REMOVE_VENDOR": "Vendor '{{ name }}' was removed", "INVALID_VENDOR_NAME": "Моля, добавете име на доставчик"}, "EDIT_ORGANIZATIONS_EXPENSE_CATEGORIES": {"ADD_EXPENSE_CATEGORY": "Category '{{ name }}' was added", "UPDATE_EXPENSE_CATEGORY": "Category '{{ name }}' was changed", "REMOVE_EXPENSE_CATEGORY": "Category '{{ name }}' was removed", "INVALID_EXPENSE_CATEGORY_NAME": "Please add a Category name"}, "EDIT_ORGANIZATIONS_POSITIONS": {"ADD_POSITION": "Position '{{ name }}' was added", "UPDATED_POSITION": "Position '{{ name }}' was changed", "REMOVE_POSITION": "Position '{{ name }}' was removed", "INVALID_POSITION_NAME": "Please add a Position name"}, "EDIT_ORGANIZATIONS_DEPARTMENTS": {"ADD_DEPARTMENT": "Department '{{ name }}' was saved", "REMOVE_DEPARTMENT": "Department '{{ name }}' was removed", "INVALID_DEPARTMENT_NAME": "Please add a Department name"}, "EDIT_ORGANIZATIONS_CLIENTS": {"ADD_CLIENT": "New client '{{ name }}' successfully added!", "REMOVE_CLIENT": "Client '{{ name }}' successfully removed!", "INVALID_CLIENT_DATA": "Please check the Name, Primary Email and Primary Phone of your client", "INVITE_CLIENT": "Invitation email sent to '{{ name }}'.", "INVITE_CLIENT_ERROR": "Some error occurred while trying to invite client.", "EMAIL_EXISTS": "This client email already exists as a user"}, "EDIT_ORGANIZATIONS_CONTACTS": {"ADD_CONTACT": "Contact '{{ name }}' was added", "UPDATE_CONTACT": "Contact '{{ name }}' was changed", "REMOVE_CONTACT": "Contact '{{ name }}' was removed", "INVALID_CONTACT_DATA": "Please check the Name, Primary Email and Primary Phone of your contact", "INVITE_CONTACT": "Invitation email sent to '{{ name }}'.", "INVITE_CONTACT_ERROR": "Some error occurred while trying to invite contact.", "EMAIL_EXISTS": "This contact email already exists as a user"}, "EDIT_ORGANIZATIONS_EMPLOYMENT_TYPES": {"ADD_EMPLOYMENT_TYPE": "Employment type '{{ name }}' was added", "UPDATE_EMPLOYMENT_TYPE": "Employment type '{{ name }}' was changed", "INVALID_EMPLOYMENT_TYPE": "Please check the Name for Employment type input", "DELETE_EMPLOYMENT_TYPE": "Employment type '{{ name }}' was removed"}, "EDIT_ORGANIZATIONS_RECURRING_EXPENSES": {"ADD_RECURRING_EXPENSE": "Recurring expense was added for `{{ name }}`", "UPDATE_RECURRING_EXPENSE": "Recurring expense was changed for '{{ name }}'", "DELETE_RECURRING_EXPENSE": "Recurring expense was removed"}, "EDIT_ORGANIZATIONS_AWARDS": {"ADD_AWARD": "New award '{{ name }}' was added", "INVALID_AWARD_NAME_YEAR": "Please check the Name and Year for Award input", "REMOVE_AWARD": "Award '{{ name }}' was removed"}, "EDIT_ORGANIZATIONS_LANGUAGES": {"ADD_LANGUAGE": "New language '{{ name }}' was added", "INVALID_LANGUAGE_NAME_LEVEL": "Please check the Name and Level for Language input", "REMOVE_LANGUAGE": "Language '{{ name }}' was removed"}, "EDIT_ORGANIZATION_DOCS": {"CREATED": "Document '{{ name }}' was added", "ERR_CREATE": "Unable to create new organization document", "ERR_LOAD": "Unable to load organization documents", "UPDATED": "Document '{{ name }}' was changed", "ERR_UPDATED": "Unable to update the selected document!", "SELECTED_DOC": "selected document", "DELETED": "Document '{{ name }}' was removed", "ERR_DELETED": "Unable to delete the selected document!"}}, "DANGER_ZONE": {"WRONG_INPUT_DATA": "Wrong input! Please try again.", "ACCOUNT_DELETED": "Your account was deleted successfully!", "ALL_DATA_DELETED": "Your data was deleted successfully!", "RECORD_TYPE": "If Yes, please type '{{ type }}' to confirm.", "TITLES": {"ACCOUNT": "REMOVE ACCOUNT", "ALL_DATA": "REMOVE ALL DATA"}}, "EVENT_TYPES": {"ADD_EVENT_TYPE": "Event type '{{ name }}' was added", "EDIT_EVENT_TYPE": "Event type '{{ name }}' was changed", "DELETE_EVENT_TYPE": "Event type '{{ name }}' was removed", "ERROR": "{{ error }}"}, "AVAILABILITY_SLOTS": {"SAVE": "Availability Slots saved", "ERROR": "{{ error }}"}}, "TIMER_TRACKER": {"IS_BILLABLE": "Is Billable", "STOP_TIMER": "Stop Timer", "START_TIMER": "Страр<PERSON>и<PERSON><PERSON><PERSON> таймер", "TIMER": "Timer", "MANUAL": "Manual", "MANUAL_NOT_ALLOW": "Manual time not allowed", "SELECT_PROJECT": "Select Project", "SELECT_TASK": "Select Task", "SELECT_CLIENT": "Select Client", "DATE": "Дата", "START_TIME": "Start Time", "END_TIME": "End Time", "DESCRIPTION": "Description", "ADD_TIME_SUCCESS": "Time was added", "VALIDATION": {"CLIENT_REQUIRED": "Please select client", "PROJECT_REQUIRED": "Please select project", "TASK_REQUIRED": "Please select task", "DESCRIPTION_REQUIRED": "Description is required"}, "VIEW_TIMESHEET": "View timesheet", "ADD_TIME": "Add Time", "TODAY": "<PERSON><PERSON><PERSON><PERSON>", "STATUS": "Проследяването на времето вече работи в {{ source }}", "VERSION": "Версия v{{ version }}", "WAIT_FOR": "Изчаква се {{ name }} да започне...", "SETUP": {"WELCOME": "Добре дошли", "TITLE": "Добре дошли в Ever Teams", "LABEL": "Ever Teams е инструмент за продуктивност, който ви помага да останете фокусирани върху работата си и по-добре да управлявате работата на вашия екип.", "TITLE_SERVER": "G<PERSON><PERSON> Инсталационен магьосник на сървъра", "LABEL_SERVER": "Приложението Gauzy Desktop предоставя пълната функционалност на Gauzy Platform, достъпна директно на вашия настолен компютър или лаптоп. Освен това, то позволява проследяване на работно време, запис на дейности и възможността да получавате напомняния / известия за проследяване.", "FUNCTIONALITIES": "Функционалности", "WHAT_WOULD_LIKE_USE": "Какво бихте желали да използвате?", "SELECT_MULTIPLE_OPTIONS": "Можете да изберете само една опция или и двете опции.", "TIME_ACTIVITY_FEATURES": "Функции за проследяване на време и дейност", "GP_FEATURES": "Функции на Gauzy Platform", "HOW_GONNA_USE_GP": "Как ще използвате платформата Gauzy?", "SELECT_ONE_OPTION": "Трябва да изберете само една от опциите по-долу.", "INTEGRATED": "Интегри<PERSON><PERSON>н", "INSTALL_GP_LOCAL": "Инсталирайте и стартирайте Gauzy Platform на вашия локален компютър.", "CUSTOM_NETWORK": "Персонализирано / Мрежово", "SEPARATE_SERVER": "Свържете се с отделен сървър в работната ви мрежа.", "LIVE": "Наживо", "CONNECTED_LIVE_SERVER": "Свързан към нашия жив сървър и използва като SAAS.", "GONNA_USE_3RD_PARTY": "Ще имате ли нужда от интеграции с трети страни?", "SELECT_MULTIPLE_OPTIONS_3RD_PARTY": "Това е възможност, можете да изберете един или и двете типа интеграции с трети страни.", "LEGAL_NOTE": "Законово: Всички продуктови имена, търговски марки, лога, търговски марки и регистрирани търговски марки са собственост на техните съответни притежатели. Ние нямаме връзка или официална подкрепа, спонсорство или подкрепа от споменатите компании, марки, продукти, сайтове или услуги на трети страни. Връзките към софтуер на трета страна се предоставят като удобство и за информационни цели само; те не представляват одобрение или одобрение на никой от продуктите, услугите или мненията на дружеството или организацията или лицето. Използваме съответните лога и търговски марки само за да рекламираме, че нашият продукт използва съответните продукти и технологии или има вградена интеграция със съответните услуги.", "SETTING": "Настройки", "READY_FOR_ADVANCED_SETTINGS": "Готови ли сте за някои напреднали настройки?", "FINAL_STEP_GP": "Това е последната стъпка, в която можете да влезете в детайлите на вашия Gauzy Platform.", "PORT": "Порт", "PORT_API": "Порт <PERSON>", "UI": "Ui", "API_HOST": "Хост API", "HOST": "Хо<PERSON>т", "DB_NAME": "Име на базата данни", "DB_PORT": "Порт на базата данни", "USER": "Потребител", "PASSWORD": "Парола", "UI_HOSTNAME": "Име на хост на потребителския интерфейс / IP адрес"}, "SETTINGS": {"SCREEN_CAPTURE": "Захващане на екран", "MONITOR": "Монитор", "AUTOMATIC_SCREEN_CAPTURE": "Автоматично захващане на екрана", "NOTIFICATION_SETTINGS": "Настройки на известията", "DESKTOP_NOTIFICATIONS": "Известия на работния плот", "SHOW_DESKTOP_NOTIF_SCREEN_CAPTURE": "Показване на известия на работния плот при захващане на екрана", "NOTIFICATION": "Известие", "SIMPLE_NOTIF": "Просто уведомление", "DETAILED_NOTIF": "Подробно уведомление", "SHOW_NOTIF_CAPTURED_IMG": "Показване на известие със заснето изображение", "SHOW_NATIVE_OS_NOTIF": "Показване на известие от собствената операционна система", "SOUND_NOTIF": "Звукови известия", "PLAY_SOUND": "Извеждане на звук при захващане на екрана", "SOUND_ENABLED": "Звукови известия активирани", "SOUND_DISABLED": "Звукови известия деактивирани", "UPDATE_ACTIVITIES": "Актуализиране на дейностите или захващането на екрана на всеки", "RANDOM_SCREENSHOT_TIME": "Случайно време за снимане на екрана", "TRACK_TIME_PC_LOCKED": "Проследяване на времето, когато компютърът е заключен", "KEEP_SYSTEM_ACTIVE": "Запазва активен системата и екрана", "PREVENT_DISPLAY_GOING_SLEEP": "Предотвратяване на изключването на екрана", "PREVENT_DISPLAY_SLEEP": "Предотвратяване на спирането на екрана", "UPDATE": "Обновяване", "AUTOMATIC_UPDATE_CHECK": "Автоматична проверка за обновяване", "ENABLE_AUTOMATIC_UPDATE_LABEL": "Активирайте автоматичната проверка за обновяване, за да се изпрати заявка за проверка на наличността на нова версия и да се извести", "SET_UPDATE_INTERVAL_DURATION": "Задайте продължителност на интервала за проверка на обновяването", "SELECT_DELAY": "Изберете забавяне", "ENABLE_AUTOMATIC_UPDATE": "Активиране на автоматичното обновяване", "UPDATE_SERVER": "Сървър за актуализация", "SELECT_DEFAULT_CDN": "Изберете сървър за актуализация по подразбиране", "TOGGLE_UPDATE_LOCALLY": "Превключване за локално актуализиране", "LOCAL_SERVER": "Лока<PERSON>ен сървър", "LOCAL_SERVER_NOTE": "Избраната директория трябва задължително да съдържа манифестите <strong>latest.yml</strong> и/или <strong>latest-mac.yml</strong> за успешна валидация на актуализацията", "OTHER_SETTINGS": "Други настройки", "ALLOW_PRERELEASE_VERSIONS": "Разрешаване на предварителни версии", "CHECK_UPDATE_APP_VERSION": "Проверете и актуализирайте версията на приложението", "UPDATE_DOWNLOADED_NOTE": "Има изтеглена нова актуализация! Моля, щракнете върху бутона Обнови сега по-долу.", "UPDATE_AVAILABLE_NOTE": "Има налична нова актуализация! Моля, щракнете върху бутона Изтегли сега по-долу.", "CHECK_UPDATE_NOTE": "Можете да проверите за актуализации, като щракнете върху бутона Провери актуализацията по-долу.", "UPDATE_LOGS": "Жу<PERSON>нал на актуализациите", "ADVANCED_SETTINGS": "Разширени настройки", "WARNING_STOP_TIMER": "Моля, спрете таймера, ако искате да промените конфигурацията", "GENERAL": "Об<PERSON>и", "API_CONFIG": "Настройки на API", "SERVER_ACCESS_CONFIG": "Конфигурация за достъп до сървъра", "API_SERVER_PORT": "Порт на сървъра на API", "UI_SERVER_PORT": "Порт на сървъра на потребителски интерфейс", "SERVER_HOSTNAME": "Име на сървъра / IP адрес", "AUTO_START_STARTUP": "Автоматично стартиране при стартиране на системата", "SERVER_TYPE": "Вид на сървъра", "LOCAL_API_PORT": "Локален порт за API", "UI_PORT": "UI порт", "SERVER_URL": "URL адрес на сървъра", "DB_CONFIG": "Настройки на базата данни", "DB_DRIVER": "Драйвер на базата данни", "STARTUP_CONFIG": "Настройки за стартиране", "AUTOMATIC_LAUNCH": "Автоматично стартиране", "MIN_ON_STARTUP": "Прибиране в трей при стартиране", "3RD_PARTY": "Трети страни", "3RD_PARTY_CONFIG": "Настройки на трета страна", "AW_PORT": "Порт Activity Watch", "VISIBLE_AW": "Видими опции на Activity Watch в прозореца", "VISIBLE_WAKATIME": "Видими опции на Wakatime в прозореца", "SIGN_IN_AS": "Влезли сте като {{ name }} ({{ email }})", "SIGN_OUT": "Излизане", "DB_PASSWORD": "Парола на базата данни", "DB_USERNAME": "Потребителско име на базата данни", "DB_HOST": "Хост на базата данни", "CAPTURE_ALL_MONITORS": "Захващане на всички монитори", "ALL_CONNECTED_MONITORS": "Всички свързани монитори", "MONITOR_CURRENT_POSITION": "позицията на текущия монитор", "CAPTURE_ACTIVE_MONITOR": "Захващане на активния монитор", "MESSAGES": {"APP_UPDATE": "Актуализация на приложението", "UPDATE_NOT_AVAILABLE": "Няма налична актуализация", "UPDATE_ERROR": "Грешка при актуализация", "UPDATE_AVAILABLE": "Има налична актуализация", "UPDATE_DOWNLOAD_COMPLETED": "Изтеглянето на актуализацията завършено", "UPDATE_DOWNLOADING": "Изтегля се актуализацията", "SERVER_CONFIG_UPDATED": "Настройките на сървъра бяха актуализирани, моля изчакайте до рестартиране на сървъра", "SERVER_RESTARTED": "Сървърът бе рестартиран успешно", "CONNECTION_SUCCEEDS": "Установена успешна връзка със сървъра {{ url }}", "DOWNLOADING_UPDATE": "Изтегляне на обновление {{ current }} MB от {{ total }} MB ->> {{ bandwidth }} KB/с"}, "TIMEZONE": "Часова зона", "TIMEZONE_LABEL": "Изборът на 'Local' показва времето във вашата местна часова зона, докато изборът на 'UTC' показва времето в универсалната UTC часова зона, използвана от системите за фактуриране и други.", "TIMEZONE_PLACEHOLDER": "Изберете 'Local' или 'UTC'.", "TIMEZONE_LOCAL": "Местно време", "TIMEZONE_UTC": "UTC", "WIDGET": "Подредба", "WIDGET_LABEL": "Този прозорец отразява състоянието на таймера, показвайки дали той работи или е спрян, и функционира като бутон"}, "WEEKLY_LIMIT_EXCEEDED": "Седмичната лимита е превишена", "OF_HRS": "От {{ limit }} часа", "TIME_TRACKER_DISABLED": "Отслежването на времето е деактивирано", "STOP_TIMER_CHANGE_CLIENT": "Моля, спрете таймера преди да промените клиента", "ADD_CONTACT": "Добави контакт", "ADD_TASK": "Добави задача", "STOP_TIMER_CHANGE_PROJECT": "Моля, спрете таймера преди да промените проекта", "STOP_TIMER_CHANGE_TASK": "Моля, спрете таймера преди да промените задачата", "STOP_TIMER_CHANGE_DESCRIPTION": "Моля, спрете таймера преди да промените описанието", "STOP_TIMER_CHANGE_TEAM": "Моля, спрете таймера, преди да промените отбора", "ACTIVITY_WATCH_INTEGRATION": "Интеграция с ActivityWatch", "LAST_CAPTURE_TAKEN": "Последната заснета екранна снимка", "REQUEST_TASK_PERMISSION": "Изисква се разрешение от администратора на организацията", "RETURN_ONLINE": "Върни се онлайн", "SEARCH": "Търси", "OPEN_SETTINGS": "Отвори настройките", "REFRESH": "Обнови", "SWITCHED_OFFLINE": "Вече сте преминали в офлайн режим", "SWITCHED_ONLINE": "Вече сте преминали в онлайн режим", "ONLINE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "OFFLINE": "О<PERSON><PERSON><PERSON><PERSON><PERSON>", "SYNCED": "Синхрон<PERSON>з<PERSON><PERSON><PERSON>н", "SYNCED_PROGRESS": "Синхронизиране в процес", "WAIT_SYNCED": "Изчакване за синхронизиране", "DIALOG": {"WARNING": "Предупреждение", "REMOVE_SCREENSHOT": "Наистина ли искате да премахнете тази снимка на екрана и дневника на дейностите?", "CHANGE_CLIENT": "Сигурни ли сте, че искате да смените клиента?", "RESUME_TIMER": "Вашият таймер продължи да работи, когато компютърът е бил заключен. Да се продължи ли таймера?", "EXIT": "Сигурни ли сте, че искате да излезете?", "STOPPED_DU_INACTIVITY": "Таймерът беше спрян поради надминаване на периода на бездействие от {{ бездействие }}. Моля, уверете се, че стартирате отново таймера, когато продължавате работата.", "EXIT_SERVER_CONFIRM": "Щракнете върху Изход, за да спрете сървъра и излезете от приложението.", "EXIT_CONFIRM": "Щракнете върху Изход, за да спрете таймера и да излезете от приложението.", "LOGOUT_CONFIRM": "Щракнете върху Изход, за да спрете таймера и да излезете от приложението.", "SELECT_UPDATE_FILES": "Моля, изберете папка с файлове за обновяване.", "UPDATE_READY": "Готово за изтегляне на актуализация", "NEW_VERSION_AVAILABLE": "Нова версия v{{ next }} е налична. Обновете приложението, като изтеглите актуализациите за v{{ current }}", "READY_INSTALL": "Готово за инсталиране на актуализация", "HAS_BEEN_DOWNLOADED": "Изтеглено е ново издание v{{ version }}. Рестартирайте приложението, за да приложите актуализациите.", "CONNECTION_DRIVER": "Връзката с {{ driver }} DB е успешна", "STILL_WORKING": "Все още работите ли?", "INACTIVITY_HANDLER": "Обработчик на неактивност", "WANT_LOGOUT": "Сигурни ли сте, че искате да излезете?", "ERROR_HANDLER": "Управление на грешки", "ERROR_OCCURRED": "Възникна грешка"}, "NO_LIMIT": "∞ Без лимит", "TASK": "Задача", "DUE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AW_CONNECTED": "Connected към ActivityWatch", "AW_DISCONNECTED": "Disconnected към ActivityWatch", "TOASTR": {"REMOVE_SCREENSHOT": "Успешно премахнахте последната снимка на екрана и дейностите", "CANT_RUN_TIMER": "В момента не може да стартирате таймера", "NOT_AUTHORIZED": "Вие нямате разрешение да работите", "ACCOUNT_DELETED": "Вашият акаунт вече е изтрит", "PROJECT_ADDED": "Проектът беше успешно добавен", "TASK_ADDED": "Задачата беше успешно добавена", "CLIENT_ADDED": "Клиентът беше успешно добавен"}, "NATIVE_NOTIFICATION": {"STOPPED_DU_INACTIVITY": "Тракерът беше спрян поради неактивност!", "SCREENSHOT_TAKEN": "Заснет скрийншот", "SCREENSHOT_REMOVED": "Успешно премахнат последен заснет скрийншот и активности", "NEW_VERSION_AVAILABLE": "Нали<PERSON>на е нова версия за {{ name }} (версия {{ version }})"}, "MENU": {"ZOOM_IN": "Приближаване", "ZOOM_OUT": "Отдалечаване", "SETTING_DEV_MODE": "Настройване на режим на разработчика", "SERVER_DEV_MODE": "Режим на разработчика на сървърната панел", "TIMER_DEV_MODE": "Режим на разработчика на времевият проследяващ", "NOW_TRACKING": "Сега се проследява време - {{ time }}", "START_TRACKING": "Започни проследяване на време", "STOP_TRACKING": "Спри проследяването на време", "OPEN_TIMER": "Отвори времевият проследяващ", "WINDOW": "Прозорец", "HELP": "Помощ", "LEARN_MORE": "Научете повече"}}, "SERVER": "Сървър", "SERVER_LOG": "Сървърни записи", "TIMESHEET": {"TODAY": "<PERSON><PERSON><PERSON><PERSON>", "DAILY": "Daily", "WEEKLY": "Weekly", "MONTHLY": "Monthly", "CALENDAR": "Calendar", "APPROVALS": "Approvals", "APPROVE_SUCCESS": "Timesheet Successfully Approved", "DENIED_SUCCESS": "Timesheet Successfully Denied", "SUBMIT_SUCCESS": "Timesheet Successfully Submitted", "UNSUBMIT_SUCCESS": "Timesheet Successfully Unsubmit", "DELETE_TIMELOG": "Are you sure you want to delete Timelog?", "SELECT_EMPLOYEE": "Select Employee", "ALL_EMPLOYEE": "Всички служители", "SELECT_SOURCE": "Select Source", "SELECT_ACTIVITY_LEVEL": "Select Activity Level", "SELECT_LOG_TYPE": "Select Log Type", "ADD_TIME_LOGS": "Add Time Logs", "EDIT_TIME_LOGS": "Edit Time Logs", "VIEW_TIME_LOGS": "Time Logs", "ADD_TIME": "Add Time", "UPDATE_TIME": "Update Time", "TILL_NOW": "Till now", "VIEW": "View", "EDIT": "Редактиране", "CLOSE": "Затвори", "DELETE": "Изтрий", "IMMUTABLE_TIME": "Immutable Time", "BULK_ACTION": "Bulk action", "LOG_TYPE": "Log type", "SOURCE": "Source", "TOTAL_TIME": "Total Time", "ACTIVITIES": "Activities", "APPROVED_AT": "Approved At", "SUBMITTED_AT": "Submitted At", "STATUS": "Статус", "SUBMIT_TIMESHEET": "Submit Timesheet", "UNSUBMIT_TIMESHEET": "Unsubmit Timesheet", "APPROVE": "Approve", "DENY": "<PERSON><PERSON>", "TIME_SPAN": "Time span", "ACTION": "Action", "EMPLOYEE": "Служител", "DURATION": "Duration", "ORGANIZATION_CONTACT": "Кли<PERSON><PERSON>т", "NO_ORGANIZATION_CONTACT": "No Client", "PROJECT": "Проект", "NO_PROJECT": "No Project", "NO_TIMELOG": "No timelogs found", "TODO": "To-do", "NO_TODO": "No To-do", "OVERLAP_MESSAGE": "This time entry overlaps activity in other projects/tasks, Which will be replaced:", "REASON": "Reason", "NOTES": "Бележки", "TIME_OVERLAPS": "Time overlaps", "TIME_TRACKING": "Time Tracking", "MEMBERS_WORKED": "Members worked", "PROJECTS_WORKED": "Projects worked", "ACTIVITY_OVER_PERIOD": "Activity over the period", "ACTIVITY_FOR_DAY": "Activity for the day", "ACTIVITY_FOR_WEEK": "Weekly Activity", "WORKED_THIS_WEEK": "Worked this week", "WORKED_OVER_PERIOD": "Worked over the period", "WORKED_FOR_DAY": "Worked for the day", "WORKED_FOR_WEEK": "Worked for the week", "TODAY_ACTIVITY": "Today's Activity", "WORKED_TODAY": "Worked today", "RECENT_ACTIVITIES": "Recent Activities", "NO_SCREENSHOT_DAY": "No screenshot for the day", "NO_SCREENSHOT_WEEK": "No screenshot for the week", "NO_SCREENSHOT_PERIOD": "No screenshot for over the period", "TASKS": "Tasks", "NO_TASK_ACTIVITY_DAY": "No task activity for the day", "NO_TASK_ACTIVITY_WEEK": "No task activity for the week", "NO_TASK_ACTIVITY_PERIOD": "No task activity for over the period", "MANUAL_TIME": "Manual Time", "NO_MANUAL_TIME_DAY": "No manual time for the day", "NO_MANUAL_TIME_WEEK": "No manual time for the week", "NO_MANUAL_TIME_PERIOD": "No manual time for over the period", "PROJECTS": "Проекти", "NO_PROJECT_ACTIVITY_DAY": "No project activity for the day", "NO_PROJECT_ACTIVITY_WEEK": "No project activity for the week", "NO_PROJECT_ACTIVITY_PERIOD": "No project activity for the period", "APPS_URLS": "Apps & Urls", "NO_APP_URL_ACTIVITY_DAY": "No app & url activity for the day", "NO_APP_URL_ACTIVITY_WEEK": "No app & url activity for the week", "NO_APP_URL_ACTIVITY_PERIOD": "No app & url activity for over the period", "DATE": "Дата", "MEMBERS": "Members", "MEMBER": "Member", "MEMBER_INFO": "Member info", "THIS_WEEK": "This week", "OVER_PERIOD": "Over the period", "NO_MEMBER_ACTIVITY_DAY": "No member activity for the day", "NO_MEMBER_ACTIVITY_WEEK": "No member activity for the week", "NO_MEMBER_ACTIVITY_PERIOD": "No member activity for over the period", "TITLE": "Title", "URL": "Url", "TIME_SPENT": "Time spent (hours)", "ACTIVITY_LEVEL": "Activity Level", "VALIDATION": {"DESCRIPTION": "Description is required", "TASK": "Please select task", "PROJECT": "Please select project", "EMPLOYEE": "Please select employee", "REASON": "Please select reason"}, "SCREENSHOTS": {"SCREENSHOTS": "Screenshots", "OF": "of", "TIME_LOG": "TimeLog"}, "RUNNING_TIMER_WARNING": "Warning. Timer is running, you can only remove screenshots if you stop timer or Inside Timer app", "DELETE_CONFIRM": "Are you sure to delete this screenshots and time log?", "VIEW_LOG": "View Log", "NO_DATA": {"DAILY_TIMESHEET": "You have not any time log yet for this day.", "WEEKLY_TIMESHEET": "You have not any time log yet for these day's.", "APPROVAL_TIMESHEET": "You don't have any timesheets for approvals yet for these day's."}, "VIEW_WINDOWS": "View windows", "VIEW_WIDGETS": "View widgets", "SOURCES": {"WEB_TIMER": "Web Timer", "DESKTOP": "Desktop Timer App", "MOBILE": "Mobile Timer App", "UPWORK": "Upwork", "HUBSTAFF": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BROWSER_EXTENSION": "Browser Extension", "TEAMS": "Екипи"}, "LAST_WORKED": "Последно работеше:"}, "ACTIVITY": {"SCREENSHOTS": "Screenshots", "PERCENT_USED": "Percent used", "TIME_SPENT": "Time spent (hours)", "APPS": "Apps", "VISITED_SITES": "Visited Sites", "NO_ACTIVITIES": "No records found. Please select date range, employee or project.", "NO_ACTIVITY": "No Activity", "TIME_AND_ACTIVITIES": "Time & Activities", "VIEW_SCREEN": "View Screen", "VIEW_INFO": "View Info", "MINUTES": "Minutes", "NO_EMPLOYEES_SELECTED": "Please select Employee", "DELETE_CONFIRM": "Are you sure you want to delete this screenshots and activities?", "VISITED_DATES": "Visited Dates", "NO_SCREENSHOT": "No Screenshot", "NO_SCREENSHOTS": "No Screenshots", "NO_RECORD_FOUND": "No records found. Please select date range, employee or project."}, "ONBOARDING": {"FIRST_ORGANIZATION": "Let's create your first organization", "COMPLETE": "You're all set!"}, "VALIDATION": {"FIELD_REQUIRED": "This field is required!", "ENTER_POSITIVE_NUMBER": "Please enter positive number!"}, "APPOINTMENTS_PAGE": {"ADD_APPOINTMENT": "Add Appointment", "EDIT_APPOINTMENT": "Edit Appointment", "SAVE_SUCCESS": "Appointment was saved", "SAVE_FAILED": "Failed to save appointment", "CANCEL_APPOINTMENT": "<PERSON>cel Appointment", "CANCEL_FAIL": "Failed to cancel appointment", "CANCEL_SUCCESS": "Appointment was cancelled", "DURATION_ERROR": "Selected Duration is not valid", "SELECT_EMPLOYEE": "Select Employee", "EMPLOYEE": "Служител", "BUFFER_TIME": "Buffer Time", "BUFFER_AT_START": "Buffer at Start", "BUFFER_AT_END": "Buffer at End", "BREAK_TIME": "Break Time", "ARE_YOU_SURE": "Are you sure? This action is irreversible."}, "EMPLOYEE_SCHEDULES_MODAL": {"EMPLOYEE": "Служител", "SLOTS_AVAILABLE": "Employee is available only for the below time slots, Do you still want to continue?", "SLOTS_UNAVAILABLE": "Employee unavailable for this time slot"}, "EVENT_TYPE_PAGE": {"EVENT_TYPE": "Event Types", "MANAGE_EVENT_TYPE": "Manage Event Type", "EVENT_NAME": "Event Type Name", "EVENT_DURATION": "Duration", "EVENT_DESCRIPTION": "Description", "ACTIVE": "Акти<PERSON><PERSON>н", "EMPLOYEE": "Служител", "YES": "Yes", "NO": "No", "DURATION_UNIT": "{{ unit }}"}, "SCHEDULE": {"DATE_SPECIFIC_AVAILABILITY": "Date Specific Availability", "SELECT_EMPLOYEE": "Моля селектирайте служител от менюто по-долу.", "RECURRING_AVAILABILITY": "Recurring Availability", "DATE_SPECIFIC_AVAILABILITY_TOOLTIP": "You can use Date specific availability to mark availability for a particular date or add exceptions to your Recurring availability pattern. The changes you make will override your Weekly recurring availability on those specific dates only.", "RECURRING_AVAILABILITY_TOOLTIP": "You can use Recurring availability to mark your availability on a weekly basis. To change your availability for specific days only, you must use Date-specific availability.", "MONDAY_FRIDAY": "Monday - Friday", "SUNDAY_THURSDAY": "Sunday - Thursday"}, "PUBLIC_APPOINTMENTS": {"BOOK_APPOINTMENT": "Book Appointment", "BOOK_APPOINTMENTS": "Book an appointment", "SELECT_EVENT_TYPES": "Please select an event type", "PICK_DATETIME": "Pick a date and time", "DURATION": "Duration:", "EVENT_TYPE": "Event Type:", "CONFIRM_APPOINTMENT": "Appointment Confirmed", "APPOINTMENT_INFO": "Appointment Information", "DETAILS": "Booking Details", "PARTICIPANTS": "Participant Emails", "HOST": "Host", "RESCHEDULE": "Reschedule Appointment", "CANCEL": "<PERSON>cel Appointment", "TIMEZONE": "Your time zone:", "CHANGE": "(Change)", "SELECT_EMPLOYEE_ERROR": "Please select an employee.", "EXPIRED_OR_CANCELLED": "**This appointment has expired or has been cancelled.", "EMAIL_SENT": "**An email has been sent to the host as well as to all the participants for this meeting.", "NO_ACTIVE_EVENT_TYpES": "There are no active event types"}, "EMAIL_TEMPLATES_PAGE": {"HEADER": "Email Templates", "SAVE": "Запази", "LABELS": {"LANGUAGE": "<PERSON>зи<PERSON> (Language)", "TEMPLATE_NAME": "Template Name", "SUBJECT": "Subject", "EMAIL_BODY": "Email Body", "EMAIL_PREVIEW": "Email Preview", "SUBJECT_PREVIEW": "Subject Preview"}, "TEMPLATE_NAMES": {"password": "Password Reset", "welcome-user": "Welcome User", "invite-organization-client": "Invite Organization Client", "email-estimate": "Email Estimate", "email-invoice": "Email Invoice", "invite-employee": "Invite Employee", "invite-user": "Invite User", "appointment-confirmation": "Appointment Confirmation", "appointment-cancellation": "Appointment Cancellation", "equipment": "Equipment Create", "equipment-request": "Equipment Request", "timesheet-overview": "Time sheet overview", "timesheet-submit": "Time Sheet Submit", "timesheet-action": "Time Sheet Actions", "timesheet-delete": "Time Sheet Delete", "time-off-report-action": "Time off policy action", "task-update": "Task Update", "candidate-schedule-interview": "Candidate Interview Schedule", "interviewer-interview-schedule": "Interviewer Schedule", "employee-join": "Employee Join", "email-reset": "<PERSON><PERSON>", "organization-team-join-request": "Organization team join request", "payment-receipt": "Касова бележка"}, "HTML_EDITOR": "HTML Editor"}, "PIPELINES_PAGE": {"HEADER": "Pipelines", "HEADER_STAGES": "Stages", "VIEW_DEALS": "View Deals", "HEADER_FORM_EDIT": "<PERSON>", "HEADER_FORM_CREATE": "Create Pipeline", "RECORD_TYPE": "pipeline \"{{ name }}\"", "ACTIVE": "Акти<PERSON><PERSON>н", "INACTIVE": "Inactive", "BROWSE": "Browse", "SEARCH": "Search", "SEARCH_PIPELINE": "Search Pipeline", "NAME": "Име", "STAGE": "Stage", "SEARCH_STAGE": "Search Stage", "STATUS": "Статус", "ALL_STATUS": "All Status", "RESET": "Reset"}, "PIPELINE_DEALS_PAGE": {"HEADER": "Pipeline Deals", "FILTER_BY_STAGE": "Filter by Stage", "RECORD_TYPE": "stage \"{{ title }}\"", "ALL_STAGES": "All Stages", "LOW": "Low", "MEDIUM": "Medium", "HIGH": "High", "DEAL_DELETED": "Deal '{{ name }}' was removed", "DEAL_EDITED": "Deal '{{ name }}' was changed", "DEAL_ADDED": "Deal '{{ name }}' was added"}, "PIPELINE_DEAL_EDIT_PAGE": {"HEADER": "Edit Deal | Pipeline: '{{ name }}'"}, "PIPELINE_DEAL_CREATE_PAGE": {"HEADER": "Create Deal | Pipeline: '{{ name }}'", "SELECT_STAGE": "Select Stage", "PROBABILITY": "Probability", "SELECT_CLIENT": "Select Client"}, "DIALOG": {"CONFIRM": "Потвърждение", "ALERT": "<PERSON><PERSON>", "DELETE_CONFIRM": "<PERSON><PERSON>"}, "SETTINGS_FILE_STORAGE": {"FILE_PROVIDER": "Доставчик на файлове", "S3": {"HEADER": "S3 Configuration", "LABELS": {"ACCESS_KEY_ID": "Access key id", "SECRET_ACCESS_KEY": "Secret access key", "REGION": "Region", "BUCKET": "Bucket"}, "PLACEHOLDERS": {"ACCESS_KEY_ID": "Access key id", "SECRET_ACCESS_KEY": "Secret access key", "REGION": "Region", "BUCKET": "Bucket"}}, "WASABI": {"HEADER": "Wasabi Configuration", "LABELS": {"ACCESS_KEY_ID": "Access key id", "SECRET_ACCESS_KEY": "Secret access key", "REGION": "Region", "BUCKET": "Bucket", "SERVICE_URL": "Service URL"}, "PLACEHOLDERS": {"ACCESS_KEY_ID": "Access key id", "SECRET_ACCESS_KEY": "Secret access key", "REGION": "Region", "BUCKET": "Bucket", "SERVICE_URL": "Service URL"}}}, "CUSTOM_SMTP_PAGE": {"HEADER": "Manage SMTP for '{{ name }}'", "HOST": "Host", "PORT": "Port", "SECURE": "Secure", "AUTH": {"USERNAME": "Потребителско име", "PASSWORD": "Парола"}}, "SMS_GATEWAY_PAGE": {"HEADER": "SMS Provider", "TWILIO": "<PERSON><PERSON><PERSON>"}, "FEATURE_PAGE": {"HEADER": "Manage Features for '{{ name }}'"}, "SETTINGS": {"EMAIL_HISTORY": {"EMAIL_ARCHIVED": "Email Archived", "RESEND": "Преизпращане", "ARCHIVE": "Archive", "HEADER": "Email History", "FROM": "From:", "TO": "To:", "DATE": "Дата:", "SUBJECT": "Subject:", "LANGUAGE": "Language:", "TEMPLATE": "Template:", "NO_EMAILS_SENT": "No emails sent.", "SYSTEM": "System", "FILTERS": {"TO": "To", "TEMPLATE_LANGUAGE": "Template/Language"}}}, "GAUZY_MAINTENANCE": "{{ companySite }} @ Maintenance", "LEGAL": {"PRIVACY_POLICY": "Privacy Policy", "TERMS_AND_CONDITIONS": "Terms & Conditions"}, "LOADING": "Loading, please hold....", "ACCOUNTING_TEMPLATES_PAGE": {"HEADER": "Accounting Templates", "TEMPLATE_NAMES": {"invoice": "Фактура", "estimate": "Estimate", "receipt": "Receipt"}}, "LOGIN_PAGE": {"TITLE": "<PERSON><PERSON>", "SUB_TITLE": "Hello! Log in with your email.", "REMEMBER_ME_TITLE": "Remember me", "DO_NOT_HAVE_ACCOUNT_TITLE": "Don't have an account?", "FORGOT_PASSWORD_TITLE": "Forgot Password?", "OR_SIGN_IN_WITH": "Или влезте с", "LABELS": {"EMAIL": "Email address:", "PASSWORD": "Password:"}, "PLACEHOLDERS": {"EMAIL": "Email address", "PASSWORD": "Парола"}, "VALIDATION": {"EMAIL_REQUIRED": "Email is required!", "EMAIL_REAL_REQUIRED": "Email should be the real one!", "PASSWORD_REQUIRED": "Password is required!", "PASSWORD_SHOULD_CONTAIN": "Password should contain from {{ minLength }} to {{ maxLength }} characters!", "PASSWORD_NO_SPACE_EDGES": "Passwords must not begin or end with spaces."}, "DEMO": {"TITLE": "Login Automatically into Demo accounts", "SUB_TITLE": "Please select account type below."}}, "FORGOT_PASSWORD_PAGE": {"TITLE": "Forgot Password", "SUB_TITLE": "Enter your email address and we’ll send a link to reset your password", "ALERT_TITLE": "Oh snap!", "ALERT_SUCCESS_TITLE": "Hooray!", "REQUEST_PASSWORD_TEXT": "Request password", "BACK_TO_LOGIN": "Back to?", "FAQ_TITLE": "FAQ", "FAQ_LEARN_MORE": "Learn more", "LABELS": {"EMAIL": "Enter your email address:"}, "PLACEHOLDERS": {"EMAIL": "Email address"}, "VALIDATION": {"EMAIL_REQUIRED": "Email is required!", "EMAIL_REAL_REQUIRED": "Email should be the real one!"}}, "PAGINATION": {"ITEMS": "Items"}, "USER_MENU": {"STATUS": "Статус", "AVAILABLE": "Available", "UNAVAILABLE": "Unavailable", "PAUSE_NOTIFICATIONS": "Pause notifications", "FOR_1_HOUR": "For 1hour", "FOR_2_HOURS": "For 2hours", "UNTIL_TOMORROW": "Until tomorrow", "CUSTOM": "Custom", "SET_AS_NOTIFICATION_SCHEDULE": "Set as notification schedule", "SET_YOURSELF_AS_AWAY": "Set yourself as away", "HOTKEYS": "Hotkeys", "HELP": "Помощ", "SIGN_OUT": "Sign Out", "PROFILE": "Profile"}, "WORKSPACES": {"MENUS": {"SING_ANOTHER_WORKSPACE": "Sign in another workspace", "CREATE_NEW_WORKSPACE": "Create a new workspace", "FIND_WORKSPACE": "Find workspace"}}, "NO_IMAGE": {"ADD_DROP": "Add or Drop Image", "AVAILABLE": "Image not available"}}