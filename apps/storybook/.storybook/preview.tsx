import React, { useState } from 'react';
import type { Preview } from '@storybook/react';
import { ClocProvider, ClocLoginDialog } from '@cloc/atoms';
import './style.css';
import { Decorator } from '@storybook/react';
import { Dialog, ThemedButton, Toaster } from '@cloc/ui';
import { RouterContext } from 'next/dist/shared/lib/router-context.shared-runtime';

const ClocDecorator: Decorator = (Story, context) => {
	return (
		<ClocProvider>
			<div className="fixed left-0  z-[49]  top-0  flex justify-between gap-4  p-4  items-center">
				<ClocLoginDialog />
			</div>

			<Story {...context} />
			<Toaster />
		</ClocProvider>
	);
};

export const decorators = [ClocDecorator];

const preview: Preview = {
	parameters: {
		controls: {
			matchers: {
				color: /(background|color)$/i,
				date: /Date$/i
			}
		}
	}
};

export default preview;
