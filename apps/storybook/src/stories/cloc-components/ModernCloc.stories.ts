import type { <PERSON>a, StoryObj } from '@storybook/react';
import { ClocProvider, ModernCloc } from '@cloc/atoms';

const meta = {
	title: 'Clock/Modern Cloc',
	component: ModernCloc,
	parameters: {
		layout: 'centered'
	}
} satisfies Meta<typeof ModernCloc>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Small: Story = {
	args: {
		size: 'sm',
		separator: ':',
		expanded: false,
		showProgress: true,
		draggable: false
	}
};

export const SmallBordered: Story = {
	args: {
		size: 'sm',
		separator: ':',
		expanded: false,
		showProgress: true,
		draggable: false,
		variant: 'bordered'
	}
};

export const SmallCustomSeparator: Story = {
	args: {
		size: 'sm',
		separator: '-',
		expanded: false,
		showProgress: true,
		draggable: false
	}
};

export const SmallWithNoProgress: Story = {
	args: {
		size: 'sm',
		separator: ':',
		expanded: false,
		showProgress: false,
		draggable: false
	}
};

export const SmallExpanded: Story = {
	args: {
		size: 'sm',
		separator: ':',
		expanded: true,
		showProgress: true,
		draggable: false
	}
};

export const SmallExpandedBordered: Story = {
	args: {
		size: 'sm',
		separator: ':',
		expanded: true,
		showProgress: true,
		draggable: false,
		variant: 'bordered'
	}
};

export const Default: Story = {
	args: {
		separator: ':',
		showProgress: true,
		expanded: false
	}
};

export const DefaultExpanded: Story = {
	args: {
		separator: ':',
		showProgress: true,
		expanded: true
	}
};

export const DefaultExpandedBorderded: Story = {
	args: {
		separator: ':',
		showProgress: true,
		expanded: true,
		variant: 'bordered'
	}
};

export const DefaultExpandedBorderdedCustomTheme: Story = {
	args: {
		separator: ':',
		showProgress: true,
		expanded: true,
		variant: 'bordered'
	}
};

export const DefaultExpandedBorderdedCustomTheme2: Story = {
	args: {
		separator: ':',
		showProgress: true,
		expanded: true,
		variant: 'bordered'
	}
};

export const DefaultExpanded1: Story = {
	args: {
		separator: ':',
		expanded: true,
		showProgress: true
	}
};
