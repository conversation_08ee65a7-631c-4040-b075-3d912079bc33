import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { ClocChart } from '@cloc/atoms';

const meta = {
	title: 'Clock/Cloc Chart',
	component: ClocChart,
	parameters: {
		layout: 'centered'
	}
} satisfies Meta<typeof ClocChart>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Cloc<PERSON>reaChart: Story = {
	args: {
		type: 'area'
	}
};

export const ClocTooltipChart: Story = {
	args: {
		type: 'tooltip'
	}
};

export const ClocLineChart: Story = {
	args: {
		type: 'line'
	}
};

export const ClocBarVerticalChart: Story = {
	args: {
		type: 'bar-vertical'
	}
};

export const ClocBarChart: Story = {
	args: {
		type: 'bar'
	}
};
