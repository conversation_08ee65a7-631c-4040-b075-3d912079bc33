import type { Meta, StoryObj } from '@storybook/react';
import { DatePicker } from '@cloc/ui';

const meta = {
	title: 'Components/DatePicker',
	component: DatePicker,
	parameters: {
		layout: 'centered'
	}
} satisfies Meta<typeof DatePicker>;

export default meta;
type Story = StoryObj<typeof meta>;

export const DefaultDatePicker: Story = {
	args: {}
};

export const DatePickerWithoutIcon: Story = {
	args: { placeholder: 'Choose a date', icon: false }
};
