import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { Calendar } from '@cloc/ui';

const meta = {
	title: 'Components/Calendar',
	component: Calendar,
	parameters: {
		layout: 'centered'
	}
} satisfies Meta<typeof Calendar>;

export default meta;
type Story = StoryObj<typeof meta>;

export const DefaultCalendar: Story = {
	args: {
		showOutsideDays: true
	}
};

export const DefaultCalendarWithNoOutDays: Story = {
	args: {
		showOutsideDays: false
	}
};
