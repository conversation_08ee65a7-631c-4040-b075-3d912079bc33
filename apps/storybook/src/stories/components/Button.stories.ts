import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { Button } from '@cloc/ui';
import { ChevronLeft } from 'lucide-react';
import { ReactNode } from 'react';

const meta = {
	title: 'Components/Button',
	component: Button,
	parameters: {
		layout: 'centered'
	},

	// Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
	args: { onClick: fn() }
} satisfies Meta<typeof Button>;

export default meta;
type Story = StoryObj<typeof meta>;

export const DefaultButton: Story = {
	args: {
		variant: 'default',
		size: 'default',
		children: 'Default Button'
	}
};

export const DestructiveButton: Story = {
	args: {
		variant: 'destructive',
		size: 'default',
		children: 'Destructive Button'
	}
};

export const OutlineButton: Story = {
	args: {
		variant: 'outline',
		size: 'default',
		children: 'Outline Button'
	}
};

export const SecondaryButton: Story = {
	args: {
		variant: 'secondary',
		size: 'default',
		children: 'Secondary Button'
	}
};

export const GhostButton: Story = {
	args: {
		variant: 'ghost',
		size: 'default',
		children: 'Ghost Button'
	}
};

export const LinkButton: Story = {
	args: {
		variant: 'link',
		size: 'default',
		children: 'Link Button'
	}
};

export const SmallButton: Story = {
	args: {
		variant: 'default',
		size: 'sm',
		children: 'Small Button'
	}
};

export const LargeButton: Story = {
	args: {
		variant: 'default',
		size: 'lg',
		children: 'Large Button'
	}
};

export const IconButton: Story = {
	args: {
		variant: 'default',
		size: 'icon',
		children: '>'
	}
};
