import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { ClocChart } from '@cloc/atoms';

const meta = {
	title: 'Components/Chart',
	component: Cloc<PERSON>hart,
	parameters: {
		layout: 'centered'
	}
} satisfies Meta<typeof ClocChart>;

export default meta;
type Story = StoryObj<typeof meta>;

export const BarChart: Story = {
	args: {
		type: 'bar'
	}
};

export const AreaChart: Story = {
	args: {
		type: 'area'
	}
};
