{"name": "@cloc/storybook", "version": "0.1.0", "license": "AGPL-3.0", "private": true, "scripts": {"dev": "storybook dev -p 6006", "build": "storybook build -o storybook-static", "build-storybook": "storybook build"}, "dependencies": {"next": "15.2.4", "react": "^19", "react-dom": "^19"}, "devDependencies": {"@chromatic-com/storybook": "^1.5.0", "@cloc/atoms": "*", "@cloc/eslint-config": "*", "@cloc/tailwind-config": "*", "@cloc/typescript-config": "*", "@cloc/ui": "*", "@storybook/addon-essentials": "^8.1.10", "@storybook/addon-interactions": "^8.1.10", "@storybook/addon-links": "^8.1.10", "@storybook/addon-onboarding": "^8.1.10", "@storybook/blocks": "^8.1.10", "@storybook/nextjs": "^8.1.10", "@storybook/react": "^8.1.10", "@storybook/test": "^8.1.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8", "storybook": "^8.1.10", "tailwindcss": "^3.4.1", "typescript": "^5"}, "resolutions": {"@storybook/addon-a11y": "8.1.10", "@storybook/addon-actions": "8.1.10", "@storybook/addon-backgrounds": "8.1.10", "@storybook/addon-controls": "8.1.10", "@storybook/addon-docs": "8.1.10", "@storybook/addon-essentials": "8.1.10", "@storybook/addon-highlight": "8.1.10", "@storybook/addon-interactions": "8.1.10", "@storybook/addon-jest": "8.1.10", "@storybook/addon-links": "8.1.10", "@storybook/addon-mdx-gfm": "8.1.10", "@storybook/addon-measure": "8.1.10", "@storybook/addon-onboarding": "8.1.10", "@storybook/addon-outline": "8.1.10", "@storybook/addon-storysource": "8.1.10", "@storybook/addon-themes": "8.1.10", "@storybook/addon-toolbars": "8.1.10", "@storybook/addon-viewport": "8.1.10", "@storybook/angular": "8.1.10", "@storybook/blocks": "8.1.10", "@storybook/builder-manager": "8.1.10", "@storybook/builder-vite": "8.1.10", "@storybook/builder-webpack5": "8.1.10", "@storybook/channels": "8.1.10", "@storybook/cli": "8.1.10", "@storybook/client-logger": "8.1.10", "@storybook/codemod": "8.1.10", "@storybook/components": "8.1.10", "@storybook/core-common": "8.1.10", "@storybook/core-events": "8.1.10", "@storybook/core-server": "8.1.10", "@storybook/core-webpack": "8.1.10", "@storybook/csf-plugin": "8.1.10", "@storybook/csf-tools": "8.1.10", "@storybook/docs-tools": "8.1.10", "@storybook/ember": "8.1.10", "@storybook/html": "8.1.10", "@storybook/html-vite": "8.1.10", "@storybook/html-webpack5": "8.1.10", "@storybook/instrumenter": "8.1.10", "@storybook/manager": "8.1.10", "@storybook/manager-api": "8.1.10", "@storybook/nextjs": "8.1.10", "@storybook/node-logger": "8.1.10", "@storybook/preact": "8.1.10", "@storybook/preact-vite": "8.1.10", "@storybook/preact-webpack5": "8.1.10", "@storybook/preset-create-react-app": "8.1.10", "@storybook/preset-html-webpack": "8.1.10", "@storybook/preset-preact-webpack": "8.1.10", "@storybook/preset-react-webpack": "8.1.10", "@storybook/preset-server-webpack": "8.1.10", "@storybook/preset-svelte-webpack": "8.1.10", "@storybook/preset-vue3-webpack": "8.1.10", "@storybook/preview": "8.1.10", "@storybook/preview-api": "8.1.10", "@storybook/react": "8.1.10", "@storybook/react-dom-shim": "8.1.10", "@storybook/react-vite": "8.1.10", "@storybook/react-webpack5": "8.1.10", "@storybook/router": "8.1.10", "@storybook/server": "8.1.10", "@storybook/server-webpack5": "8.1.10", "@storybook/source-loader": "8.1.10", "@storybook/svelte": "8.1.10", "@storybook/svelte-vite": "8.1.10", "@storybook/svelte-webpack5": "8.1.10", "@storybook/sveltekit": "8.1.10", "@storybook/telemetry": "8.1.10", "@storybook/test": "8.1.10", "@storybook/theming": "8.1.10", "@storybook/types": "8.1.10", "@storybook/vue3": "8.1.10", "@storybook/vue3-vite": "8.1.10", "@storybook/vue3-webpack5": "8.1.10", "@storybook/web-components": "8.1.10", "@storybook/web-components-vite": "8.1.10", "@storybook/web-components-webpack5": "8.1.10", "sb": "8.1.10", "storybook": "8.1.10", "jackspeak": "2.1.1"}, "packageManager": "yarn@1.22.19"}