---
kind: Service
apiVersion: v1
metadata:
    name: ever-cloc-dev-api-lb
    annotations:
        service.beta.kubernetes.io/do-loadbalancer-name: 'apidev.cloc.ai'
        service.beta.kubernetes.io/do-loadbalancer-protocol: 'http2'
        service.beta.kubernetes.io/do-loadbalancer-http2-ports: '443'
        # Replace with your Certificate Id. You can get a list of Ids with 'doctl compute certificate list'
        service.beta.kubernetes.io/do-loadbalancer-certificate-id: "e9d49a9b-ea0b-48bc-bff1-b0465e77cd63"
        service.beta.kubernetes.io/do-loadbalancer-size-slug: 'lb-small'
        service.beta.kubernetes.io/do-loadbalancer-hostname: 'apidev.cloc.ai'
spec:
    type: LoadBalancer
    selector:
        app: ever-cloc-dev-api
    ports:
        - name: http
          protocol: TCP
          port: 443
          targetPort: 3000

---
kind: Deployment
apiVersion: apps/v1
metadata:
    name: ever-cloc-dev-api
spec:
    replicas: 1
    selector:
        matchLabels:
            app: ever-cloc-dev-api
    template:
        metadata:
            labels:
                app: ever-cloc-dev-api
        spec:
            containers:
                - name: ever-cloc-dev-api
                  image: registry.digitalocean.com/ever/gauzy-api-demo:latest
                  resources:
                      requests:
                          memory: '1536Mi'
                          cpu: '1000m'
                      limits:
                          memory: '2048Mi'
                  env:
                      - name: API_HOST
                        value: 0.0.0.0
                      - name: DEMO
                        value: 'true'
                      - name: CLOUD_PROVIDER
                        value: '$CLOUD_PROVIDER'
                      - name: NODE_ENV
                        value: 'development'
                      - name: ADMIN_PASSWORD_RESET
                        value: 'true'
                      - name: LOG_LEVEL
                        value: 'info'
                      - name: SENTRY_DSN
                        value: '$SENTRY_DSN'
                      - name: SENTRY_HTTP_TRACING_ENABLED
                        value: '$SENTRY_HTTP_TRACING_ENABLED'
                      - name: SENTRY_PROFILING_ENABLED
                        value: '$SENTRY_PROFILING_ENABLED'
                      - name: SENTRY_POSTGRES_TRACKING_ENABLED
                        value: '$SENTRY_POSTGRES_TRACKING_ENABLED'
                      - name: API_BASE_URL
                        value: 'https://apidev.cloc.ai'
                      - name: CLIENT_BASE_URL
                        value: 'https://demo.cloc.ai'
                      - name: EXPRESS_SESSION_SECRET
                        value: 'gauzy'
                      - name: JWT_SECRET
                        value: 'secretKey'
                      - name: JWT_REFRESH_TOKEN_SECRET
                        value: 'refreshSecretKey'
                      - name: JWT_REFRESH_TOKEN_EXPIRATION_TIME
                        value: '86400'
                      - name: OTEL_ENABLED
                        value: '$OTEL_ENABLED'
                      - name: OTEL_EXPORTER_OTLP_TRACES_ENDPOINT
                        value: '$OTEL_EXPORTER_OTLP_TRACES_ENDPOINT'
                      - name: OTEL_EXPORTER_OTLP_HEADERS
                        value: '$OTEL_EXPORTER_OTLP_HEADERS'
                  ports:
                      - containerPort: 3000
                        protocol: TCP
