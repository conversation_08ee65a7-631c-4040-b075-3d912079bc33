{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"composite": false, "declaration": true, "declarationMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "inlineSources": false, "isolatedModules": true, "moduleResolution": "bundler", "preserveWatchOutput": true, "skipLibCheck": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true}, "exclude": ["node_modules"]}