'use client';

import { PolarAngleAxis, PolarGrid, Radar, RadarChart as RechartRadar<PERSON>hart } from 'recharts';

import { ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from '../chart';
import { IChartProps } from '@cloc/types';
import { ChartContainer } from '../chart-container';
import React from 'react';
import { useClocContext } from '@lib/context/cloc-context';
import { generateRandomColors } from '@cloc/ui';

const RadarChart: React.FC<IChartProps> = ({ color, data, config, title, description, footer }) => {
	const { config: contextConfig } = useClocContext();
	const finalConfig = config || contextConfig;

	const hasData = data.length > 0;
	const colors = color && hasData ? generateRandomColors(color, Object.keys(data[0]).length) : '';

	return (
		<ChartContainer config={finalConfig} title={title} description={description} footer={footer}>
			<RechartRadarChart data={data}>
				<ChartTooltip cursor={false} content={<ChartTooltipContent />} />

				{/* <Radar dataKey="desktop" fill="var(--color-desktop)" fillOpacity={0.6} /> */}
				<ChartLegend
					className="w-full flex flex-wrap justify-center items-center"
					content={<ChartLegendContent />}
				/>
				{hasData &&
					Object.keys(data[0]).map((item, index) => {
						return (
							index !== 0 && (
								<React.Fragment key={index}>
									<PolarAngleAxis
										tickFormatter={(value) => value.split('-')[2] + '/' + value.split('-')[1]}
										dataKey={'day'}
									/>

									<PolarGrid gridType="circle" />

									<Radar
										dataKey={item}
										fill={colors[index - 1]}
										// type="bump"
										fillOpacity={0.6}
										// strokeWidth={1}
										// radius={15}
										opacity={0.6}
									>
										{/* <LabelList
											position="top"
											offset={12}
											className="fill-foreground"
											fontSize={12}
											content={<ChartCustomLabel width={3} x={3} y={5} />}
										/> */}
									</Radar>
								</React.Fragment>
							)
						);
					})}
			</RechartRadarChart>
		</ChartContainer>
	);
};

RadarChart.displayName = 'RadarChart';

export { RadarChart };
