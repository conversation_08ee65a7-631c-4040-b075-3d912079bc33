'use client';

import { <PERSON>, <PERSON><PERSON><PERSON> as Rechart<PERSON>ie<PERSON><PERSON> } from 'recharts';

import { ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from '../chart';
import { IChartProps } from '@cloc/types';
import { ChartContainer } from '../chart-container';
import { useClocContext } from '@lib/context/cloc-context';
import { generateRandomColors } from '@cloc/ui';

const PieChart: React.FC<IChartProps> = ({ data, config, color }) => {
	const { config: contextConfig } = useClocContext();
	const finalConfig = config || contextConfig;

	const hasData = data.length > 0;
	const colors = color && hasData ? generateRandomColors(color, Object.keys(data[0]).length) : '';

	return (
		<ChartContainer config={finalConfig}>
			<RechartPieChart>
				<ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />
				{/* <Pie data={chartData} dataKey="visitors" nameKey="browser" stroke="0" /> */}
				<ChartLegend
					className="w-full flex flex-wrap justify-center items-center"
					content={<ChartLegendContent />}
				/>
				{hasData &&
					Object.keys(data[0]).map((item, index) => {
						return (
							index !== 0 && (
								<>
									<Pie
										key={index}
										dataKey={'day'}
										nameKey={item}
										data={data}
										fill={colors[index - 1]}
										label
										// type="bump"
										// fillOpacity={0.6}
										stroke={'0'}
										// radius={15}
									>
										{/* <LabelList
											position="top"
											offset={12}
											className="fill-foreground"
											fontSize={12}
											content={<ChartCustomLabel width={3} x={3} y={5} />}
										/> */}
									</Pie>
								</>
							)
						);
					})}
			</RechartPieChart>
		</ChartContainer>
	);
};
PieChart.displayName = 'PieChart';
export { PieChart };
