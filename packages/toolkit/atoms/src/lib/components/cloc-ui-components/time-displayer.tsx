import { ITimerDisplayerProps } from '@cloc/types';
import { cn, getTimeDigits } from '@cloc/ui';
import { useClocContext } from '@lib/context/cloc-context';

export const TimeDisplayer = ({
	separator = ':',
	fontSize = 12,
	fontWeight,
	fontFamily,
	className
}: ITimerDisplayerProps) => {
	const { hours, minutes, seconds } = useClocContext();

	const style: React.CSSProperties | undefined = {
		fontSize,
		fontFamily,
		fontWeight
	};

	return (
		<span style={style} className={cn(className)}>
			{getTimeDigits(hours)}
			{separator}
			{getTimeDigits(minutes)}
			{separator}
			{getTimeDigits(seconds)}
		</span>
	);
};
