import { Select, Tooltip } from '@cloc/ui';
import { ICurrentClocState } from '@cloc/types';
import { useTranslation } from 'react-i18next';
import { useClocContext } from '@lib/context/cloc-context';

export const ClocTimerClientSelect = ({ size }: { size?: 'default' | 'sm' | 'lg' | null }) => {
	const {
		organizationClients,
		currentClocState,
		setCurrentClocState,
		isRunning,
		loadings: { clientsLoading }
	} = useClocContext();
	const { t } = useTranslation();

	return (
		<div className=" flex flex-col gap-2 ">
			<label htmlFor="client" className="text-sm ">
				{t('COMMON.client')} :
			</label>
			<Tooltip message={isRunning ? t('WARNING.stop_timer_before_selecting', { item: t('COMMON.client') }) : ''}>
				<Select
					loading={clientsLoading}
					name="client"
					disabled={isRunning}
					size={size}
					placeholder={t('INPUT.client_select.select_client')}
					value={currentClocState?.clientId || undefined}
					defaultValue={currentClocState?.clientId || undefined}
					values={
						organizationClients && organizationClients[0]
							? organizationClients?.map((client) => {
									return { value: client.id, label: client.name };
								})
							: undefined
					}
					onValueChange={(e) => {
						setCurrentClocState((state: ICurrentClocState) => {
							return {
								...state,
								clientId: e
							};
						});
					}}
				/>
			</Tooltip>
		</div>
	);
};
