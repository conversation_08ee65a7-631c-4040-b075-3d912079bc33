import { Select, Tooltip } from '@cloc/ui';
import { ICurrentClocState } from '@cloc/types';

import { useTranslation } from 'react-i18next';
import { useClocContext } from '@lib/context/cloc-context';

export const ClocTimerProjectSelect = ({ size }: { size?: 'default' | 'sm' | 'lg' | null }) => {
	const {
		loadings: { projectsLoading },
		isRunning,
		organizationProjects,
		currentClocState,
		setCurrentClocState
	} = useClocContext();
	const { t } = useTranslation();

	return (
		<div className=" flex flex-col gap-2 ">
			<label htmlFor="project" className="text-sm ">
				{t('COMMON.project')} :
			</label>
			<Tooltip message={isRunning ? t('WARNING.stop_timer_before_selecting', { item: t('COMMON.project') }) : ''}>
				<Select
					loading={projectsLoading}
					name="project"
					disabled={isRunning}
					size={size}
					placeholder={t('INPUT.project_select.select_project')}
					value={currentClocState?.projectId || undefined}
					defaultValue={currentClocState?.projectId || undefined}
					values={
						organizationProjects && organizationProjects[0]
							? organizationProjects?.map((project) => {
									return { value: project.id, label: project.name };
								})
							: undefined
					}
					onValueChange={(e) => {
						setCurrentClocState((state: ICurrentClocState) => {
							return {
								...state,
								projectId: e
							};
						});
					}}
				/>
				{/* {projectsLoading && <LoaderCircle className="animate-spin" />} */}
			</Tooltip>
		</div>
	);
};
