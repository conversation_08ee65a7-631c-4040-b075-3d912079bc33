import { Select, Tooltip } from '@cloc/ui';
import { ICurrentClocState } from '@cloc/types';
import { useTranslation } from 'react-i18next';
import { useClocContext } from '@lib/context/cloc-context';

export const ClocTimerTaskSelect = ({ size }: { size?: 'default' | 'sm' | 'lg' | null }) => {
	const {
		tasks,
		isRunning,
		setCurrentClocState,
		currentClocState,
		loadings: { userTasksLoading }
	} = useClocContext();
	const { t } = useTranslation();

	return (
		<div className=" flex flex-col gap-2 ">
			<label htmlFor="task" className="text-sm ">
				{t('COMMON.task')} :
			</label>
			<Tooltip message={isRunning ? t('WARNING.stop_timer_before_selecting', { item: t('COMMON.task') }) : ''}>
				<Select
					loading={userTasksLoading}
					name="task"
					size={size}
					placeholder={t('INPUT.task_select.select_task')}
					disabled={isRunning}
					value={currentClocState?.taskId || undefined}
					defaultValue={currentClocState?.taskId || undefined}
					values={
						tasks && tasks.length > 0
							? tasks?.map((task) => {
									return { label: task.title.substring(0, 30), value: task.id };
								})
							: undefined
					}
					onValueChange={(e) => {
						setCurrentClocState((state: ICurrentClocState) => {
							return {
								...state,
								taskId: e
							};
						});
					}}
				/>
			</Tooltip>
		</div>
	);
};
