import { Select, Tooltip } from '@cloc/ui';
import { ICurrentClocState } from '@cloc/types';
import { useTranslation } from 'react-i18next';
import { useClocContext } from '@lib/context/cloc-context';
export const ClocTimerTeamSelect = ({ size }: { size?: 'default' | 'sm' | 'lg' | null }) => {
	const {
		isRunning,
		organizationTeams: teams,
		setCurrentClocState,
		currentClocState,
		loadings: { organisationsLoading }
	} = useClocContext();

	const { t } = useTranslation();

	return (
		<div className=" flex flex-col gap-2 ">
			<label htmlFor="team" className="text-sm ">
				{t('COMMON.team')} :
			</label>
			<Tooltip
				placement="auto"
				message={isRunning ? t('WARNING.stop_timer_before_selecting', { item: t('COMMON.team') }) : ''}
			>
				<Select
					loading={organisationsLoading}
					name="team"
					size={size}
					placeholder={t('INPUT.team_select.select_team')}
					disabled={isRunning}
					value={currentClocState?.organizationTeamId || undefined}
					defaultValue={currentClocState?.organizationTeamId || undefined}
					values={
						teams
							? teams?.items.map((team) => {
									return { label: team.name, value: team.id };
								})
							: undefined
					}
					onValueChange={(e) => {
						setCurrentClocState((state: ICurrentClocState) => {
							return {
								...state,
								organizationTeamId: e
							};
						});
					}}
				/>
			</Tooltip>
		</div>
	);
};
