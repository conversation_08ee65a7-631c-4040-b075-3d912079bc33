import { Select } from '@cloc/ui';
import { useClocContext } from '@lib/context/cloc-context';

export const ClocActiveOrganizationSelector = ({
	size,
	label,
	className
}: {
	size?: 'default' | 'sm' | 'lg' | null;
	label?: string;
	className?: string;
}) => {
	const {
		userOrganizations,
		loadings: { userOrganizationsLoading },
		selectedOrganization,
		setSelectedOrganization
	} = useClocContext();

	return (
		<div className=" flex flex-col gap-2 ">
			{label && (
				<label htmlFor="organization" className="text-sm ">
					{label}
				</label>
			)}
			<Select
				loading={userOrganizationsLoading}
				className={className}
				name="organization"
				size={size}
				placeholder={'Select organization'}
				disabled={userOrganizationsLoading}
				value={selectedOrganization}
				// values={[
				// 	{
				// 		label: 'Team 1 (4)',
				// 		value: 'id1'
				// 		// icon: <Avatar  className="size-6 text-xs" src="" fallback="AB" title="Avatar" />
				// 	},
				// 	{
				// 		label: 'Team 2 (4)',
				// 		value: 'id2'
				// 		// icon: <Avatar  className="size-6 text-xs" src="" fallback="AB" title="Avatar" />
				// 	},
				// 	{
				// 		label: 'Team 3 (4)',
				// 		value: 'id3'
				// 		// icon: <Avatar  className="size-6 text-xs" src="" fallback="AB" title="Avatar" />
				// 	}
				// ]}
				values={
					userOrganizations?.items?.length
						? userOrganizations.items.map((elt) => {
								return {
									label: elt.organization.name,
									value: elt.organizationId
								};
							})
						: undefined
				}
				onValueChange={(e: string) => {
					setSelectedOrganization(e);
				}}
			/>
		</div>
	);
};
