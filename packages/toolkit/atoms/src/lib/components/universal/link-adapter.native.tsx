import React from 'react';

export interface LinkAdapterProps {
    href: string;
    children: React.ReactNode;
    className?: string;
    target?: string;
    rel?: string;
    onClick?: () => void;
}

export const LinkAdapter = ({ href, children, ...rest }: LinkAdapterProps) => (
    <a href={href} {...rest}>
        {children}
    </a>
);

export function detectFramework(): 'native' | 'web' | 'server' {
    if (typeof window !== 'undefined' && (window as any).ReactNativeWebView) {
        return 'native';
    }
    if (typeof window === 'undefined') {
        return 'server';
    }
    return 'web';
}
