import { Progress } from '@cloc/ui';
import { cn } from '@cloc/ui';
import { cva, VariantProps } from 'class-variance-authority';
import { Theme } from 'theme-ui';
import React from 'react';
import { Member } from '@cloc/types';
import { BasicTimer } from '../../time-trackers/basic-timer';
import { useTranslation } from 'react-i18next';
import { defaultTheme } from '@lib/themes/themes';

// Variants with responsive design
const basicClocMemberVariants = cva(
	'backdrop-blur-sm p-6 bg-white dark:bg-gray-800 relative rounded-xl flex flex-col justify-start gap-5 shadow-2xl custom-scroll',
	{
		variants: {
			variant: {
				default: '',
				bordered: 'border-2 border-secondaryColor'
			},
			size: {
				default: 'w-full max-w-[1200px]',
				sm: 'w-full max-w-[300px] text-sm p-4',
				lg: 'w-full max-w-[40rem] px-10'
			}
		},
		defaultVariants: {
			variant: 'default',
			size: 'default'
		}
	}
);

export interface IBasicClocMemberProps
	extends React.HTMLAttributes<HTMLDivElement>,
		VariantProps<typeof basicClocMemberVariants> {
	values?: Member[];
	className?: string;
	classNameTitle?: string;
	draggable?: boolean;
	showProgress?: boolean;
	showTime?: boolean;
	theme?: Theme<{}>;
	risizable?: boolean;
	renderItem?: React.ReactNode;
}

function BasicClocMember({
	values,
	variant = 'default',
	size = 'default',
	draggable = true,
	className,
	showProgress = false,
	theme = defaultTheme,
	renderItem,
	showTime,
	classNameTitle,
	title,
	...props
}: IBasicClocMemberProps): React.JSX.Element {
	const { t } = useTranslation();

	return (
		<div
			{...props}
			className={cn(basicClocMemberVariants({ variant, size, className }), 'custom-scroll w-full handle resize')}
		>
			<div className="flex flex-col w-full gap-4">
				<div className="flex justify-between items-center w-full">
					<span
						className={cn(
							'font-medium text-base md:text-lg text-slate-800 dark:text-slate-400 ',
							classNameTitle
						)}
					>
						{t('COMMON.members_activities')}
					</span>
					<button className="font-normal text-sm text-[#3826A6] dark:text-[#786bcd] hover:underline">
						{t('COMMON.view_more')}
					</button>
				</div>
				{values &&
					values.map(
						({ color, label, progress }, index) =>
							!renderItem && (
								<div key={index} className="flex items-center w-full gap-4">
									<div className="flex items-center gap-2">
										<div
											style={{ backgroundColor: color }}
											className="w-2.5 h-2.5 rounded-full"
										></div>
										<span className="text-sm text-slate-800 dark:text-slate-400">{label}</span>
									</div>
									{showProgress && (
										<>
											<Progress value={progress} className="w-full" />
											<span className="text-sm w-9 text-left text-slate-800 dark:text-slate-400">
												{progress}%
											</span>
										</>
									)}
									{showTime && (
										<div className="text-sm">
											<BasicTimer icon readonly className="py-0 text-sm" />
										</div>
									)}
								</div>
							)
					)}
			</div>
		</div>
	);
}

BasicClocMember.displayName = 'BasicClocMember';
export { BasicClocMember };
