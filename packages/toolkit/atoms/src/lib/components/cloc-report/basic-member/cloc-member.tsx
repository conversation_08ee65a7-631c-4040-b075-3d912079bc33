'use client';

import { useClocContext } from '@lib/context/cloc-context';
import { BasicClocMember } from './basic-cloc-member';
interface MemberClocProps {
	size?: 'default' | 'sm' | 'lg';
	showProgress?: boolean;
	showTime?: boolean;
	className?: string;
}
export function MemberCloc({ size, className, showProgress, showTime }: MemberClocProps) {
	const { members, seconds } = useClocContext();

	const dynamicValues = members?.items.map((member) => ({
		label: member.fullName ?? '',
		progress: calculatePercentage(seconds),
		color: '#eab308'
	}));

	return (
		<BasicClocMember
			className={className}
			showTime={showTime}
			showProgress={showProgress}
			values={dynamicValues}
			variant="bordered"
			size={size}
			classNameTitle="font-bold"
		/>
	);
}

function calculatePercentage(seconds: number) {
	const percentage = (seconds * 60) / 100;
	return Math.floor(percentage);
}
