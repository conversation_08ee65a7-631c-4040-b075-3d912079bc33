import { VariantProps } from 'class-variance-authority';
import { ClocBaseList, clocBaseListVariants } from './base';
import { useTranslation } from 'react-i18next';
import { useClocContext } from '@lib/context/cloc-context';

interface IClocAppsUrlListProps extends VariantProps<typeof clocBaseListVariants> {
	className?: string;
}

const ClocAppsUrlList = ({ variant, size, className }: IClocAppsUrlListProps) => {
	const {
		activitiesStats,
		loadings: { activitiesStatsLoading }
	} = useClocContext();
	const { t } = useTranslation();

	return (
		<ClocBaseList
			stats={{ data: activitiesStats, loading: activitiesStatsLoading }}
			title={t('COMMON.apps_and_url')}
			className={className}
			variant={variant}
			size={size}
		/>
	);
};

export { ClocAppsUrlList };
