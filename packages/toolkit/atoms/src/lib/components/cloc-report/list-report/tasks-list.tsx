import { VariantProps } from 'class-variance-authority';
import { useClocContext } from '@lib/context/cloc-context';
import { ClocBaseList, clocBaseListVariants } from './base';
import { useTranslation } from 'react-i18next';

interface IClocTasksListProps extends VariantProps<typeof clocBaseListVariants> {
	className?: string;
}

const ClocTasksList = ({ variant, size, className }: IClocTasksListProps) => {
	const {
		tasksStats,
		loadings: { tasksStatsLoading }
	} = useClocContext();
	const { t } = useTranslation();
	return (
		<ClocBaseList
			stats={{ data: tasksStats, loading: tasksStatsLoading }}
			title={t('COMMON.task')}
			className={className}
			variant={variant}
			size={size}
		/>
	);
};

export { ClocTasksList };
