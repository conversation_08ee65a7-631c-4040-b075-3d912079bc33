import { cn, formatTime, Progress } from '@cloc/ui';
import { cva, VariantProps } from 'class-variance-authority';
import { SpinOverlayLoader } from '@components/loaders/spin-overlay-loader';
import { IProjectsStats } from '@hooks/useProjectsStatistics';
import { ITasksStats } from '@hooks/useTasksStatistics';
import { IActivitiesStats } from '@cloc/types';
import { useTranslation } from 'react-i18next';
import { ClocTimerFooter } from '@components/layouts/footers/component-footer';

interface ItemDetailProps {
	name: string;
	percentage: number;
	time: number;
}

interface IClocBaseListProps extends VariantProps<typeof clocBaseListVariants> {
	className?: string;
	title: string;
	stats: { data: IProjectsStats | ITasksStats | IActivitiesStats | null; loading: boolean };
}

/**
 * A single item in the cloc report list, showing the name, percentage,
 * a progress bar, and the total time worked.
 *
 * @param {{ name: string, percentage: number, time: number }} props
 * @returns {ReactElement}
 */
const ItemDetail: React.FC<ItemDetailProps> = ({ name, percentage, time }) => {
	return (
		<div className="flex justify-between items-start text-xs">
			<span className="text-sm w-[55%] font-normal">{name}</span>
			<div className="flex gap-4 w-[40%] justify-between items-center">
				<span className="text-sm">{percentage.toFixed(2)}%</span>
				<Progress className=" h-2" value={Math.ceil(percentage)} />
				<span className="text-sm font-mono">{formatTime(time)}</span>
			</div>
		</div>
	);
};

const clocBaseListVariants = cva(
	' rounded-xl shadow-2xl rounded-2xl bg-white dark:bg-black text-gray-900 dark:text-gray-100 py-4 ',
	{
		variants: {
			variant: {
				default: '',
				bordered: 'border-2 border-secondaryColor'
			},
			size: {
				default: 'w-[600px]',
				sm: 'w-[400px]',
				lg: 'w-[800px]'
			}
		},
		defaultVariants: {
			variant: 'default',
			size: 'default'
		}
	}
);

/**
 * A component that renders a base list for cloc reports with customizable variants and sizes.
 *
 * @param {Object} props - The properties object.
 * @param {'default' | 'bordered'} [props.variant='default'] - The visual variant of the list.
 * @param {'default' | 'sm' | 'lg'} [props.size='default'] - The size variant of the list.
 * @param {string} props.className - Additional CSS class names.
 * @param {string} props.type - The type of report being displayed, used for the header text.
 * @param {{ data: Array<Object> | null, loading: boolean }} [props.stats={ data: null, loading: false }] - The stats data to be displayed in the list and a loading state.
 * @returns {ReactElement} The rendered component displaying a list with a header and item details.
 */

const ClocBaseList = ({
	variant,
	size,
	className,
	title,
	stats = { data: null, loading: false }
}: IClocBaseListProps) => {
	const { t } = useTranslation();
	return (
		<div className={cn(clocBaseListVariants({ variant, size, className }))}>
			<div className="flex justify-between items-center border-b border-gray-200 dark:border-gray-800 rounded-t-xl px-6  pb-2  font-bold">
				<h1 className="capitalize">{title}</h1>
				{/* <ClocDateRangePicker /> */}
			</div>

			<div className={'flex flex-col p-6 gap-4 relative max-h-[300px] overflow-y-auto custom-scroll'}>
				{stats.loading && <SpinOverlayLoader />}
				{stats.data &&
					stats.data.map((item, index) => (
						<ItemDetail
							key={index}
							name={'name' in item ? item.name : item.title}
							percentage={Number(item.durationPercentage)}
							time={Number(item.duration)}
						/>
					))}
				{(!stats.data || stats?.data.length === 0) && (
					<p className="text-center text-gray-400">{t('NO_DATA.no_data_available')}</p>
				)}
			</div>
			<ClocTimerFooter />
		</div>
	);
};

export { ClocBaseList, ItemDetail, clocBaseListVariants };
