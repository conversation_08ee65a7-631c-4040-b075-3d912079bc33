'use client';

import HomeLayout from '@/components/layout/home-layout';
import {
	BasicClocReport,
	ClocAppsUrlList,
	ClocLoginDialog,
	ClocProjectsList,
	ClocRegistrationForm,
	ClocTasksList,
	ClocThemeToggle,
	DailyActivityDisplayer,
	DailyWorkedTimeDisplayer,
	ModernCloc,
	useClocContext,
	WeeklyActivityDisplayer,
	WeeklyWorkedTimeDisplayer,
	WorkedProjectDisplayer
} from '@cloc/atoms';
import { Button, Dialog, ThemedButton } from '@cloc/ui';
import { useTranslations } from 'next-intl';
import { JSX, Suspense } from 'react';

function ClocShowCase(): JSX.Element {
	const { authenticatedUser: user } = useClocContext();
	const t = useTranslations('Cloc');

	return (
		<HomeLayout>
			<div className="my-20 flex flex-col gap-6 items-center ">
				<h1 className=" font-bold text-center text-7xl tracking-tighter">{t('title')}</h1>
				<p className="text-center text-[#777777] dark:text-gray-400">{t('description')}</p>
				<ClocThemeToggle />
				<div className={'flex gap-3 items-center'}>
					<DailyWorkedTimeDisplayer />
					<WeeklyWorkedTimeDisplayer />
					<WeeklyActivityDisplayer />
					<DailyActivityDisplayer />
					<WorkedProjectDisplayer />
				</div>
				{!user && (
					<div className="flex gap-6">
						<Dialog
							trigger={
								<ThemedButton size={'lg'} className="min-w-40">
									{t('get_started')}
								</ThemedButton>
							}
						>
							<ClocRegistrationForm />
						</Dialog>

						<ClocLoginDialog
							trigger={
								<Button
									size={'lg'}
									variant={'outline'}
									className="relative min-w-40 hover:scale-105 transition-all"
								>
									Login
								</Button>
							}
						/>
					</div>
				)}
			</div>
			<div className="flex m-5 box-border gap-2 flex-wrap  justify-center sm:items-start">
				<ModernCloc expanded={false} showProgress={true} />
				<BasicClocReport type="area" size={'default'} />
				<Suspense fallback={<div>Loading...</div>}>
					<ModernCloc expanded showProgress />
				</Suspense>
				<ClocProjectsList />
				<ClocTasksList />
				<ClocAppsUrlList />

				{/* This is a place where you can test component before putting them */}

				{/* atoms or any other package */}
			</div>
		</HomeLayout>
	);
}

export default ClocShowCase;
