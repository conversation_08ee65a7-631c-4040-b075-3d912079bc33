'use client';

import { ReactElement, useEffect, useState } from 'react';
import { useAccessToken } from '@cloc/atoms';
import { authLogin, createCompleteAccount } from '@cloc/api';

/**
 * Component that handles Cloc authentication after successful local login
 * This runs on the dashboard and other authenticated pages
 */
export function ClocAuthHandler(): ReactElement | null {
	const { setAccessToken, accessToken } = useAccessToken();
	const [isProcessing, setIsProcessing] = useState(false);
	const [hasProcessed, setHasProcessed] = useState(false);

	useEffect(() => {
		const handleClocAuth = async () => {
			// Skip if already has Cloc token or already processed
			if (accessToken || hasProcessed || isProcessing) {
				return;
			}

			// Check if we have login credentials stored from recent login
			const recentLogin = sessionStorage.getItem('recent-login-credentials');
			if (!recentLogin) {
				return;
			}

			setIsProcessing(true);

			try {
				const { email, password, timestamp } = JSON.parse(recentLogin);

				// Only process if the login was recent (within last 30 seconds)
				if (Date.now() - timestamp > 30000) {
					sessionStorage.removeItem('recent-login-credentials');
					return;
				}

				// Try to authenticate with Cloc
				console.log('Attempting Cloc login for:', email);
				const clocResult = await authLogin({ email, password });

				if (clocResult && 'token' in clocResult) {
					// Use Cloc's native session management
					setAccessToken(clocResult.token);
				} else {
					// Create complete account with full organizational setup
					console.log('Creating complete Cloc account...');
					const setupResult = await createCompleteAccount({
						fullName: email.includes('@') ? email.split('@')[0] : email, // Could enhance to get from local user data
						email,
						password,
						confirmPassword: password
					});

					if ('token' in setupResult) {
						setAccessToken(setupResult.token);
					} else {
						console.warn('❌ Cloc account setup failed:', setupResult.error || setupResult.message);
					}
				}

				// Clear the stored credentials after processing
				sessionStorage.removeItem('recent-login-credentials');
			} catch (error) {
				console.warn('❌ Cloc authentication failed:', error);
			} finally {
				setIsProcessing(false);
				setHasProcessed(true);
			}
		};

		handleClocAuth();
	}, [accessToken, hasProcessed, isProcessing, setAccessToken]);

	// This component renders nothing visible
	return null;
}
