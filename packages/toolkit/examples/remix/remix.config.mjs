/** @type {import('@remix-run/dev').AppConfig} */
export default {
	appDirectory: 'app',
	assetsBuildDirectory: 'public/build',
	future: {
		/* any enabled future flags */
	},
	browserNodeBuiltinsPolyfill: {
		modules: {
			util: true,
			buffer: true,
			process: true,
			crypto: true,
			stream: true,
			path: true,
			os: true,
			fs: true,
			events: true
		}
	},
	ignoredRouteFiles: ['**/*.css'],
	publicPath: '/build/',
	serverBuildPath: 'build/index.js'
};
