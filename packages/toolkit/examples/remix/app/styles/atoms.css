*,
:after,
:before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgba(59, 130, 246, .5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
    --tw-contain-style:
}

::backdrop {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgba(59, 130, 246, .5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
    --tw-contain-style:
}

/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/
*,
:after,
:before {
    border: 0 solid #e5e7eb;
    box-sizing: border-box
}

:after,
:before {
    --tw-content: ""
}

:host,
html {
    -webkit-text-size-adjust: 100%;
    font-feature-settings: normal;
    -webkit-tap-highlight-color: transparent;
    font-family: ui-sans-serif, system-ui, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
    font-variation-settings: normal;
    line-height: 1.5;
    -moz-tab-size: 4;
    -o-tab-size: 4;
    tab-size: 4
}

body {
    line-height: inherit;
    margin: 0
}

hr {
    border-top-width: 1px;
    color: inherit;
    height: 0
}

abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-size: inherit;
    font-weight: inherit
}

a {
    color: inherit;
    text-decoration: inherit
}

b,
strong {
    font-weight: bolder
}

code,
kbd,
pre,
samp {
    font-feature-settings: normal;
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
    font-size: 1em;
    font-variation-settings: normal
}

small {
    font-size: 80%
}

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sub {
    bottom: -.25em
}

sup {
    top: -.5em
}

table {
    border-collapse: collapse;
    border-color: inherit;
    text-indent: 0
}

button,
input,
optgroup,
select,
textarea {
    font-feature-settings: inherit;
    color: inherit;
    font-family: inherit;
    font-size: 100%;
    font-variation-settings: inherit;
    font-weight: inherit;
    letter-spacing: inherit;
    line-height: inherit;
    margin: 0;
    padding: 0
}

button,
select {
    text-transform: none
}

button,
input:where([type=button]),
input:where([type=reset]),
input:where([type=submit]) {
    -webkit-appearance: button;
    background-color: transparent;
    background-image: none
}

:-moz-focusring {
    outline: auto
}

:-moz-ui-invalid {
    box-shadow: none
}

progress {
    vertical-align: baseline
}

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
    height: auto
}

[type=search] {
    -webkit-appearance: textfield;
    outline-offset: -2px
}

::-webkit-search-decoration {
    -webkit-appearance: none
}

::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit
}

summary {
    display: list-item
}

blockquote,
dd,
dl,
figure,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
p,
pre {
    margin: 0
}

fieldset {
    margin: 0
}

fieldset,
legend {
    padding: 0
}

menu,
ol,
ul {
    list-style: none;
    margin: 0;
    padding: 0
}

dialog {
    padding: 0
}

textarea {
    resize: vertical
}

input::-moz-placeholder,
textarea::-moz-placeholder {
    color: #9ca3af;
    opacity: 1
}

input::placeholder,
textarea::placeholder {
    color: #9ca3af;
    opacity: 1
}

[role=button],
button {
    cursor: pointer
}

:disabled {
    cursor: default
}

audio,
canvas,
embed,
iframe,
img,
object,
svg,
video {
    display: block;
    vertical-align: middle
}

img,
video {
    height: auto;
    max-width: 100%
}

[hidden]:where(:not([hidden=until-found])) {
    display: none
}

:root {
    --scroll-background: #fff;
    --scroll-foreground: #e0e3ea;
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 47.4% 11.2%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 0% 80%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;
    --ring: 215 20.2% 65.1%;
    --radius: 0.5rem
}

/* Add theme-ui and ClocButton specific CSS variables and styles */
:root {
    /* ClocButton gradient colors */
    --primaryColor: #3826A6;
    --secondaryColor: #A11DB1;
    --mainColor: linear-gradient(135deg, #3826A6 0%, #A11DB1 100%);

    /* Theme-ui color mappings */
    --textColor: #000000;
    --backgroundColor: #ffffff;
    --borderColor: #e5e7eb;
}

.dark {
    --textColor: #ffffff;
    --backgroundColor: #000000;
    --borderColor: #374151;
}

/* ClocButton gradient utilities */
.from-primaryColor {
    --tw-gradient-from: #3826A6 var(--tw-gradient-from-position);
    --tw-gradient-to: rgba(56, 38, 166, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.to-secondaryColor {
    --tw-gradient-to: #A11DB1 var(--tw-gradient-to-position);
}

/* Theme-ui sx styles fallback */
[sx] {
    background: var(--mainColor, linear-gradient(135deg, #3826A6 0%, #A11DB1 100%)) !important;
}

.dark {
    --scroll-background: #000;
    --scroll-foreground: #303030;
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;
    --muted: 223 47% 11%;
    --muted-foreground: 215.4 16.3% 56.9%;
    --accent: 216 34% 17%;
    --accent-foreground: 210 40% 98%;
    --popover: 224 71% 4%;
    --popover-foreground: 215 20.2% 65.1%;
    --border: 216 34% 17%;
    --input: 216 34% 17%;
    --card: 224 71% 4%;
    --card-foreground: 213 31% 91%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 1.2%;
    --secondary: 222.2 47.4% 11.2%;
    --secondary-foreground: 210 40% 98%;
    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;
    --ring: 216 34% 17%;
    --radius: 0.5rem
}

.custom-scroll::-webkit-scrollbar {
    cursor: pointer;
    display: block;
    height: 4px;
    width: 4px
}

.custom-scroll::-webkit-scrollbar-track {
    background: var(--scroll-background);
    border-radius: 1px
}

.custom-scroll::-webkit-scrollbar-thumb {
    background: var(--scroll-foreground);
    border-radius: 1px
}

@supports not selector(::-webkit-scrollbar) {
    .custom-scroll {
        scrollbar-color: var(--scroll-foreground) var(--scroll-background)
    }
}

.invisible {
    visibility: hidden
}

.absolute {
    position: absolute
}

.relative {
    position: relative
}

.inset-0 {
    inset: 0
}

.-bottom-0\.5 {
    bottom: -.125rem
}

.bottom-0 {
    bottom: 0
}

.left-0 {
    left: 0
}

.left-1\/2 {
    left: 50%
}

.left-\[12\%\] {
    left: 12%
}

.left-\[6\%\] {
    left: 6%
}

.right-0 {
    right: 0
}

.right-3 {
    right: .75rem
}

.right-\[-5px\] {
    right: -5px
}

.top-0 {
    top: 0
}

.top-1 {
    top: .25rem
}

.top-1\/2 {
    top: 50%
}

.top-10 {
    top: 2.5rem
}

.top-3 {
    top: .75rem
}

.top-4 {
    top: 1rem
}

.top-6 {
    top: 1.5rem
}

.top-8 {
    top: 2rem
}

.-z-10 {
    z-index: -10
}

.z-10 {
    z-index: 10
}

.z-20 {
    z-index: 20
}

.z-50 {
    z-index: 50
}

.z-\[1000\] {
    z-index: var(--z-modal)
}

.row-span-1 {
    grid-row: span 1/span 1
}

.mx-auto {
    margin-left: auto;
    margin-right: auto
}

.my-0\.5 {
    margin-bottom: .125rem;
    margin-top: .125rem
}

.-ml-1 {
    margin-left: -.25rem
}

.mb-1 {
    margin-bottom: .25rem
}

.mb-2 {
    margin-bottom: .5rem
}

.mb-3 {
    margin-bottom: .75rem
}

.mb-4 {
    margin-bottom: 1rem
}

.mb-5 {
    margin-bottom: 1.25rem
}

.mb-6 {
    margin-bottom: 1.5rem
}

.ml-2 {
    margin-left: .5rem
}

.ml-5 {
    margin-left: 1.25rem
}

.ml-6 {
    margin-left: 1.5rem
}

.mr-1 {
    margin-right: .25rem
}

.mr-2 {
    margin-right: .5rem
}

.mr-3 {
    margin-right: .75rem
}

.mt-1 {
    margin-top: .25rem
}

.mt-10 {
    margin-top: 2.5rem
}

.mt-2 {
    margin-top: .5rem
}

.mt-4 {
    margin-top: 1rem
}

.mt-5 {
    margin-top: 1.25rem
}

.mt-6 {
    margin-top: 1.5rem
}

.block {
    display: block
}

.flex {
    display: flex
}

.inline-flex {
    display: inline-flex
}

.table {
    display: table
}

.grid {
    display: grid
}

.hidden {
    display: none
}

.aspect-square {
    aspect-ratio: 1/1
}

.aspect-video {
    aspect-ratio: 16/9
}

.size-14 {
    height: 3.5rem;
    width: 3.5rem
}

.size-4 {
    height: 1rem;
    width: 1rem
}

.size-5 {
    height: 1.25rem;
    width: 1.25rem
}

.size-6 {
    height: 1.5rem;
    width: 1.5rem
}

.size-\[106\%\] {
    height: 106%;
    width: 106%
}

.h-0 {
    height: 0
}

.h-0\.5 {
    height: .125rem
}

.h-1 {
    height: .25rem
}

.h-10 {
    height: 2.5rem
}

.h-14 {
    height: 3.5rem
}

.h-2 {
    height: .5rem
}

.h-2\.5 {
    height: .625rem
}

.h-20 {
    height: 5rem
}

.h-28 {
    height: 7rem
}

.h-3 {
    height: .75rem
}

.h-4 {
    height: 1rem
}

.h-5 {
    height: 1.25rem
}

.h-6 {
    height: 1.5rem
}

.h-7 {
    height: 1.75rem
}

.h-8 {
    height: 2rem
}

.h-\[250px\] {
    height: 250px
}

.h-\[300px\] {
    height: 300px
}

.h-\[350px\] {
    height: 350px
}

.h-\[600px\] {
    height: 600px
}

.h-full {
    height: 100%
}

.max-h-\[400px\] {
    max-height: 400px
}

.min-h-52 {
    min-height: 13rem
}

.w-0 {
    width: 0
}

.w-1 {
    width: .25rem
}

.w-10 {
    width: 2.5rem
}

.w-14 {
    width: 3.5rem
}

.w-16 {
    width: 4rem
}

.w-2 {
    width: .5rem
}

.w-2\.5 {
    width: .625rem
}

.w-20 {
    width: 5rem
}

.w-28 {
    width: 7rem
}

.w-3 {
    width: .75rem
}

.w-32 {
    width: 8rem
}

.w-4 {
    width: 1rem
}

.w-40 {
    width: 10rem
}

.w-44 {
    width: 11rem
}

.w-5 {
    width: 1.25rem
}

.w-6 {
    width: 1.5rem
}

.w-64 {
    width: 16rem
}

.w-7 {
    width: 1.75rem
}

.w-8 {
    width: 2rem
}

.w-9 {
    width: 2.25rem
}

.w-\[100px\] {
    width: 100px
}

.w-\[200px\] {
    width: 200px
}

.w-\[220px\] {
    width: 220px
}

.w-\[280px\] {
    width: 280px
}

.w-\[300px\] {
    width: 300px
}

.w-\[40\%\] {
    width: 40%
}

.w-\[400px\] {
    width: 400px
}

.w-\[500px\] {
    width: 500px
}

.w-\[55\%\] {
    width: 55%
}

.w-\[600px\] {
    width: 600px
}

.w-\[700px\] {
    width: 700px
}

.w-\[76\%\] {
    width: 76%
}

.w-\[800px\] {
    width: 800px
}

.w-\[88\%\] {
    width: 88%
}

.w-auto {
    width: auto
}

.w-fit {
    width: -moz-fit-content;
    width: fit-content
}

.w-full {
    width: 100%
}

.min-w-28 {
    min-width: 7rem
}

.min-w-52 {
    min-width: 13rem
}

.min-w-\[100px\] {
    min-width: 100px
}

.min-w-\[1200px\] {
    min-width: 1200px
}

.min-w-\[150px\] {
    min-width: 150px
}

.min-w-\[15rem\] {
    min-width: 15rem
}

.min-w-\[20rem\] {
    min-width: 20rem
}

.min-w-\[300px\] {
    min-width: 300px
}

.min-w-\[400px\] {
    min-width: 400px
}

.min-w-\[8rem\] {
    min-width: 8rem
}

.min-w-full {
    min-width: 100%
}

.max-w-48 {
    max-width: 12rem
}

.max-w-\[1000px\] {
    max-width: 1000px
}

.max-w-\[1200px\] {
    max-width: 1200px
}

.max-w-\[300px\] {
    max-width: 300px
}

.max-w-\[395px\] {
    max-width: 395px
}

.max-w-\[40rem\] {
    max-width: 40rem
}

.max-w-\[755px\] {
    max-width: 755px
}

.max-w-fit {
    max-width: -moz-fit-content;
    max-width: fit-content
}

.max-w-sm {
    max-width: 24rem
}

.flex-1 {
    flex: 1 1 0%
}

.flex-shrink-0,
.shrink-0 {
    flex-shrink: 0
}

.basis-1\/2 {
    flex-basis: 50%
}

.-translate-x-1\/2,
.-translate-x-2\/4 {
    --tw-translate-x: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.-translate-y-1\/2,
.-translate-y-2\/4 {
    --tw-translate-y: -50%
}

.-rotate-90,
.-translate-y-1\/2,
.-translate-y-2\/4 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.-rotate-90 {
    --tw-rotate: -90deg
}

.rotate-90 {
    --tw-rotate: 90deg
}

.rotate-90,
.scale-0 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.scale-0 {
    --tw-scale-x: 0;
    --tw-scale-y: 0
}

.scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1
}

.scale-100,
.scale-105 {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.scale-105 {
    --tw-scale-x: 1.05;
    --tw-scale-y: 1.05
}

.transform {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.animate-spin {
    animation: spin 1s linear infinite
}

.cursor-grab {
    cursor: grab
}

.cursor-not-allowed {
    cursor: not-allowed
}

.cursor-pointer {
    cursor: pointer
}

.resize {
    resize: both
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr))
}

.flex-col {
    flex-direction: column
}

.flex-wrap {
    flex-wrap: wrap
}

.items-start {
    align-items: flex-start
}

.items-end {
    align-items: flex-end
}

.items-center {
    align-items: center
}

.items-stretch {
    align-items: stretch
}

.justify-start {
    justify-content: flex-start
}

.justify-end {
    justify-content: flex-end
}

.justify-center {
    justify-content: center
}

.justify-between {
    justify-content: space-between
}

.justify-around {
    justify-content: space-around
}

.gap-1 {
    gap: .25rem
}

.gap-1\.5 {
    gap: .375rem
}

.gap-2 {
    gap: .5rem
}

.gap-3 {
    gap: .75rem
}

.gap-4 {
    gap: 1rem
}

.gap-5 {
    gap: 1.25rem
}

.gap-x-2 {
    -moz-column-gap: .5rem;
    column-gap: .5rem
}

.gap-y-2 {
    row-gap: .5rem
}

.gap-y-4 {
    row-gap: 1rem
}

.-space-x-2>:not([hidden])~:not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-left: calc(-.5rem*(1 - var(--tw-space-x-reverse)));
    margin-right: calc(-.5rem*var(--tw-space-x-reverse))
}

.space-x-2>:not([hidden])~:not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-left: calc(.5rem*(1 - var(--tw-space-x-reverse)));
    margin-right: calc(.5rem*var(--tw-space-x-reverse))
}

.space-x-3>:not([hidden])~:not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-left: calc(.75rem*(1 - var(--tw-space-x-reverse)));
    margin-right: calc(.75rem*var(--tw-space-x-reverse))
}

.space-y-2>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-bottom: calc(.5rem*var(--tw-space-y-reverse));
    margin-top: calc(.5rem*(1 - var(--tw-space-y-reverse)))
}

.space-y-4>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-bottom: calc(1rem*var(--tw-space-y-reverse));
    margin-top: calc(1rem*(1 - var(--tw-space-y-reverse)))
}

.overflow-auto {
    overflow: auto
}

.overflow-hidden {
    overflow: hidden
}

.overflow-scroll {
    overflow: scroll
}

.overflow-y-scroll {
    overflow-y: scroll
}

.whitespace-nowrap {
    white-space: nowrap
}

.\!rounded-\[8px\] {
    border-radius: 8px !important
}

.\!rounded-full {
    border-radius: 9999px !important
}

.rounded {
    border-radius: .25rem
}

.rounded-2xl {
    border-radius: 1rem
}

.rounded-3xl {
    border-radius: 1.5rem
}

.rounded-\[14px\] {
    border-radius: 14px
}

.rounded-\[2px\] {
    border-radius: 2px
}

.rounded-full {
    border-radius: 9999px
}

.rounded-lg {
    border-radius: var(--radius)
}

.rounded-md {
    border-radius: calc(var(--radius) - 2px)
}

.rounded-none {
    border-radius: 0
}

.rounded-xl {
    border-radius: .75rem
}

.rounded-t-xl {
    border-top-left-radius: .75rem;
    border-top-right-radius: .75rem
}

.\!border {
    border-width: 1px !important
}

.border {
    border-width: 1px
}

.border-2 {
    border-width: 2px
}

.border-\[0\.75px\] {
    border-width: .75px
}

.border-\[1\.5px\] {
    border-width: 1.5px
}

.border-\[2px\] {
    border-width: 2px
}

.border-b {
    border-bottom-width: 1px
}

.border-b-\[50px\] {
    border-bottom-width: 50px
}

.border-l-\[15px\] {
    border-left-width: 15px
}

.border-r-\[15px\] {
    border-right-width: 15px
}

.border-t {
    border-top-width: 1px
}

.border-solid {
    border-style: solid
}

.border-dashed {
    border-style: dashed
}

.border-none {
    border-style: none
}

.border-\[\#14b8a6\] {
    --tw-border-opacity: 1;
    border-color: rgb(20 184 166/var(--tw-border-opacity, 1))
}

.border-\[\#22AE83\] {
    --tw-border-opacity: 1;
    border-color: rgb(34 174 131/var(--tw-border-opacity, 1))
}

.border-\[\#A16BFB\] {
    --tw-border-opacity: 1;
    border-color: rgb(161 107 251/var(--tw-border-opacity, 1))
}

.border-\[--color-border\] {
    border-color: var(--color-border)
}

.border-border\/50 {
    border-color: hsl(var(--border)/.5)
}

.border-gray-200 {
    --tw-border-opacity: 1;
    border-color: rgb(229 231 235/var(--tw-border-opacity, 1))
}

.border-gray-300 {
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219/var(--tw-border-opacity, 1))
}

.border-gray-500 {
    --tw-border-opacity: 1;
    border-color: rgb(107 114 128/var(--tw-border-opacity, 1))
}

.border-gray-600 {
    --tw-border-opacity: 1;
    border-color: rgb(75 85 99/var(--tw-border-opacity, 1))
}

.border-gray-700 {
    --tw-border-opacity: 1;
    border-color: rgb(55 65 81/var(--tw-border-opacity, 1))
}

.border-gray-800 {
    --tw-border-opacity: 1;
    border-color: rgb(31 41 55/var(--tw-border-opacity, 1))
}

.border-input {
    border-color: hsl(var(--input))
}

.border-purple-600 {
    --tw-border-opacity: 1;
    border-color: rgb(147 51 234/var(--tw-border-opacity, 1))
}

.border-transparent {
    border-color: transparent
}

.border-white {
    --tw-border-opacity: 1;
    border-color: rgb(255 255 255/var(--tw-border-opacity, 1))
}

.border-b-\[\#8156C9\]\/40 {
    border-bottom-color: rgba(129, 86, 201, .4)
}

.border-b-gray-300 {
    --tw-border-opacity: 1;
    border-bottom-color: rgb(209 213 219/var(--tw-border-opacity, 1))
}

.border-l-transparent {
    border-left-color: transparent
}

.border-r-transparent {
    border-right-color: transparent
}

.bg-\[\#050011\] {
    --tw-bg-opacity: 1;
    background-color: rgb(5 0 17/var(--tw-bg-opacity, 1))
}

.bg-\[\#14b8a6\]\/15 {
    background-color: rgba(20, 184, 166, .15)
}

.bg-\[\#14b8a6\]\/80 {
    background-color: rgba(20, 184, 166, .8)
}

.bg-\[\#1b005d\] {
    --tw-bg-opacity: 1;
    background-color: rgb(27 0 93/var(--tw-bg-opacity, 1))
}

.bg-\[\#22AE83\]\/15 {
    background-color: rgba(34, 174, 131, .15)
}

.bg-\[\#22AE83\]\/80 {
    background-color: rgba(34, 174, 131, .8)
}

.bg-\[\#A16BFB\] {
    --tw-bg-opacity: 1;
    background-color: rgb(161 107 251/var(--tw-bg-opacity, 1))
}

.bg-\[\#A16BFB\]\/15 {
    background-color: rgba(161, 107, 251, .15)
}

.bg-\[\#A16BFB\]\/80 {
    background-color: rgba(161, 107, 251, .8)
}

.bg-\[\#E7E7EA\] {
    --tw-bg-opacity: 1;
    background-color: rgb(231 231 234/var(--tw-bg-opacity, 1))
}

.bg-\[--color-bg\] {
    background-color: var(--color-bg)
}

.bg-background {
    background-color: hsl(var(--background))
}

.bg-black {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 0/var(--tw-bg-opacity, 1))
}

.bg-black\/30 {
    background-color: rgba(0, 0, 0, .3)
}

.bg-blue-950 {
    --tw-bg-opacity: 1;
    background-color: rgb(23 37 84/var(--tw-bg-opacity, 1))
}

.bg-destructive {
    background-color: hsl(var(--destructive))
}

.bg-gray-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246/var(--tw-bg-opacity, 1))
}

.bg-gray-200 {
    --tw-bg-opacity: 1;
    background-color: rgb(229 231 235/var(--tw-bg-opacity, 1))
}

.bg-gray-300\/70 {
    background-color: rgba(209, 213, 219, .7)
}

.bg-gray-800 {
    --tw-bg-opacity: 1;
    background-color: rgb(31 41 55/var(--tw-bg-opacity, 1))
}

.bg-gray-900 {
    --tw-bg-opacity: 1;
    background-color: rgb(17 24 39/var(--tw-bg-opacity, 1))
}

.bg-green-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(220 252 231/var(--tw-bg-opacity, 1))
}

.bg-green-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(34 197 94/var(--tw-bg-opacity, 1))
}

.bg-inherit {
    background-color: inherit
}

.bg-orange-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(234 88 12/var(--tw-bg-opacity, 1))
}

.bg-primary {
    --tw-bg-opacity: 1;
    background-color: rgb(56 38 166/var(--tw-bg-opacity, 1))
}

.bg-purple-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(168 85 247/var(--tw-bg-opacity, 1))
}

.bg-purple-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(147 51 234/var(--tw-bg-opacity, 1))
}

.bg-red-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(220 38 38/var(--tw-bg-opacity, 1))
}

.bg-red-700 {
    --tw-bg-opacity: 1;
    background-color: rgb(185 28 28/var(--tw-bg-opacity, 1))
}

.bg-secondary {
    background-color: hsl(var(--secondary))
}

.bg-transparent {
    background-color: transparent
}

.bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity, 1))
}

.bg-opacity-20 {
    --tw-bg-opacity: 0.2
}

.bg-gradient-to-br {
    background-image: linear-gradient(to bottom right, var(--tw-gradient-stops))
}

.from-green-500 {
    --tw-gradient-from: #22c55e var(--tw-gradient-from-position);
    --tw-gradient-to: rgba(34, 197, 94, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-purple-700 {
    --tw-gradient-from: #7e22ce var(--tw-gradient-from-position);
    --tw-gradient-to: rgba(126, 34, 206, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.from-teal-500 {
    --tw-gradient-from: #14b8a6 var(--tw-gradient-from-position);
    --tw-gradient-to: rgba(20, 184, 166, 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)
}

.to-green-800 {
    --tw-gradient-to: #166534 var(--tw-gradient-to-position)
}

.to-purple-900 {
    --tw-gradient-to: #581c87 var(--tw-gradient-to-position)
}

.to-teal-800 {
    --tw-gradient-to: #115e59 var(--tw-gradient-to-position)
}

.fill-foreground {
    fill: hsl(var(--foreground))
}

.stroke-\[\#DE437B\] {
    stroke: #de437b
}

.stroke-gray-400 {
    stroke: #9ca3af
}

.object-cover {
    -o-object-fit: cover;
    object-fit: cover
}

.p-0 {
    padding: 0
}

.p-1 {
    padding: .25rem
}

.p-2 {
    padding: .5rem
}

.p-3 {
    padding: .75rem
}

.p-4 {
    padding: 1rem
}

.p-5 {
    padding: 1.25rem
}

.p-6 {
    padding: 1.5rem
}

.\!px-0 {
    padding-left: 0 !important;
    padding-right: 0 !important
}

.\!py-0 {
    padding-bottom: 0 !important;
    padding-top: 0 !important
}

.px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem
}

.px-2 {
    padding-left: .5rem;
    padding-right: .5rem
}

.px-2\.5 {
    padding-left: .625rem;
    padding-right: .625rem
}

.px-3 {
    padding-left: .75rem;
    padding-right: .75rem
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem
}

.px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem
}

.px-8 {
    padding-left: 2rem;
    padding-right: 2rem
}

.py-0 {
    padding-bottom: 0;
    padding-top: 0
}

.py-1 {
    padding-bottom: .25rem;
    padding-top: .25rem
}

.py-1\.5 {
    padding-bottom: .375rem;
    padding-top: .375rem
}

.py-2 {
    padding-bottom: .5rem;
    padding-top: .5rem
}

.py-4 {
    padding-bottom: 1rem;
    padding-top: 1rem
}

.pb-2 {
    padding-bottom: .5rem
}

.pb-3 {
    padding-bottom: .75rem
}

.pb-4 {
    padding-bottom: 1rem
}

.pb-7 {
    padding-bottom: 1.75rem
}

.pl-1 {
    padding-left: .25rem
}

.pt-3 {
    padding-top: .75rem
}

.text-left {
    text-align: left
}

.text-center {
    text-align: center
}

.text-right {
    text-align: right
}

.font-mono {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace
}

.text-2xl {
    font-size: 1.5rem;
    line-height: 2rem
}

.text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem
}

.text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem
}

.text-\[0\.6rem\] {
    font-size: .6rem
}

.text-\[10px\] {
    font-size: 10px
}

.text-base {
    font-size: 1rem;
    line-height: 1.5rem
}

.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem
}

.text-sm {
    font-size: .875rem;
    line-height: 1.25rem
}

.text-sm\/6 {
    font-size: .875rem;
    line-height: 1.5rem
}

.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem
}

.text-xs {
    font-size: .75rem;
    line-height: 1rem
}

.font-bold {
    font-weight: 700
}

.font-light {
    font-weight: 300
}

.font-medium {
    font-weight: 500
}

.font-normal {
    font-weight: 400
}

.font-semibold {
    font-weight: 600
}

.font-thin {
    font-weight: 100
}

.uppercase {
    text-transform: uppercase
}

.capitalize {
    text-transform: capitalize
}

.tabular-nums {
    --tw-numeric-spacing: tabular-nums;
    font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction)
}

.leading-none {
    line-height: 1
}

.tracking-tight {
    letter-spacing: -.025em
}

.tracking-wide {
    letter-spacing: .025em
}

.text-\[\#14b8a6\] {
    --tw-text-opacity: 1;
    color: rgb(20 184 166/var(--tw-text-opacity, 1))
}

.text-\[\#14b8a6\]\/30 {
    color: rgba(20, 184, 166, .3)
}

.text-\[\#22AE83\] {
    --tw-text-opacity: 1;
    color: rgb(34 174 131/var(--tw-text-opacity, 1))
}

.text-\[\#22AE83\]\/30 {
    color: rgba(34, 174, 131, .3)
}

.text-\[\#3826A6\] {
    --tw-text-opacity: 1;
    color: rgb(56 38 166/var(--tw-text-opacity, 1))
}

.text-\[\#7FE4C5\] {
    --tw-text-opacity: 1;
    color: rgb(127 228 197/var(--tw-text-opacity, 1))
}

.text-\[\#A16BFB\] {
    --tw-text-opacity: 1;
    color: rgb(161 107 251/var(--tw-text-opacity, 1))
}

.text-\[\#A16BFB\]\/30 {
    color: rgba(161, 107, 251, .3)
}

.text-\[\#DE437B\] {
    --tw-text-opacity: 1;
    color: rgb(222 67 123/var(--tw-text-opacity, 1))
}

.text-black {
    --tw-text-opacity: 1;
    color: rgb(0 0 0/var(--tw-text-opacity, 1))
}

.text-blue-500 {
    --tw-text-opacity: 1;
    color: rgb(59 130 246/var(--tw-text-opacity, 1))
}

.text-destructive {
    color: hsl(var(--destructive))
}

.text-destructive-foreground {
    color: hsl(var(--destructive-foreground))
}

.text-foreground {
    color: hsl(var(--foreground))
}

.text-gray-400 {
    --tw-text-opacity: 1;
    color: rgb(156 163 175/var(--tw-text-opacity, 1))
}

.text-gray-500 {
    --tw-text-opacity: 1;
    color: rgb(107 114 128/var(--tw-text-opacity, 1))
}

.text-gray-700 {
    --tw-text-opacity: 1;
    color: rgb(55 65 81/var(--tw-text-opacity, 1))
}

.text-gray-800 {
    --tw-text-opacity: 1;
    color: rgb(31 41 55/var(--tw-text-opacity, 1))
}

.text-gray-900 {
    --tw-text-opacity: 1;
    color: rgb(17 24 39/var(--tw-text-opacity, 1))
}

.text-green-500 {
    --tw-text-opacity: 1;
    color: rgb(34 197 94/var(--tw-text-opacity, 1))
}

.text-green-800 {
    --tw-text-opacity: 1;
    color: rgb(22 101 52/var(--tw-text-opacity, 1))
}

.text-inherit {
    color: inherit
}

.text-muted-foreground {
    color: hsl(var(--muted-foreground))
}

.text-primary {
    --tw-text-opacity: 1;
    color: rgb(56 38 166/var(--tw-text-opacity, 1))
}

.text-primary-foreground {
    color: hsl(var(--primary-foreground))
}

.text-purple-500 {
    --tw-text-opacity: 1;
    color: rgb(168 85 247/var(--tw-text-opacity, 1))
}

.text-purple-900 {
    --tw-text-opacity: 1;
    color: rgb(88 28 135/var(--tw-text-opacity, 1))
}

.text-red-400 {
    --tw-text-opacity: 1;
    color: rgb(248 113 113/var(--tw-text-opacity, 1))
}

.text-red-500 {
    --tw-text-opacity: 1;
    color: rgb(239 68 68/var(--tw-text-opacity, 1))
}

.text-red-600 {
    --tw-text-opacity: 1;
    color: rgb(220 38 38/var(--tw-text-opacity, 1))
}

.text-red-800 {
    --tw-text-opacity: 1;
    color: rgb(153 27 27/var(--tw-text-opacity, 1))
}

.text-secondary {
    color: hsl(var(--secondary))
}

.text-secondary-foreground {
    color: hsl(var(--secondary-foreground))
}

.text-slate-400 {
    --tw-text-opacity: 1;
    color: rgb(148 163 184/var(--tw-text-opacity, 1))
}

.text-slate-500 {
    --tw-text-opacity: 1;
    color: rgb(100 116 139/var(--tw-text-opacity, 1))
}

.text-slate-800 {
    --tw-text-opacity: 1;
    color: rgb(30 41 59/var(--tw-text-opacity, 1))
}

.text-teal-500 {
    --tw-text-opacity: 1;
    color: rgb(20 184 166/var(--tw-text-opacity, 1))
}

.text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity, 1))
}

.text-white\/40 {
    color: hsla(0, 0%, 100%, .4)
}

.underline {
    text-decoration-line: underline
}

.line-through {
    text-decoration-line: line-through
}

.underline-offset-4 {
    text-underline-offset: 4px
}

.accent-purple-600 {
    accent-color: #9333ea
}

.opacity-0 {
    opacity: 0
}

.opacity-50 {
    opacity: .5
}

.shadow-2xl {
    --tw-shadow: 0 25px 50px -12px rgba(0, 0, 0, .25);
    --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color)
}

.shadow-2xl,
.shadow-\[0px_2\.472px_9\.887px_rgba\(0\2c 0\2c 0\2c 0\.16\)\] {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-\[0px_2\.472px_9\.887px_rgba\(0\2c 0\2c 0\2c 0\.16\)\] {
    --tw-shadow: 0px 2.472px 9.887px rgba(0, 0, 0, .16);
    --tw-shadow-colored: 0px 2.472px 9.887px var(--tw-shadow-color)
}

.shadow-lg {
    --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, .1), 0 4px 6px -4px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color)
}

.shadow-lg,
.shadow-md {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-md {
    --tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -2px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color)
}

.shadow-none {
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000
}

.shadow-none,
.shadow-sm {
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.shadow-sm {
    --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);
    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color)
}

.shadow-xl {
    --tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, .1), 0 8px 10px -6px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.outline-none {
    outline: 2px solid transparent;
    outline-offset: 2px
}

.outline {
    outline-style: solid
}

.filter {
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.backdrop-blur-sm {
    --tw-backdrop-blur: blur(4px);
    -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)
}

.transition {
    transition-duration: .15s;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.transition-all {
    transition-duration: .15s;
    transition-property: all;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.transition-colors {
    transition-duration: .15s;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.delay-200 {
    transition-delay: .2s
}

.duration-200 {
    transition-duration: .2s
}

.duration-300 {
    transition-duration: .3s
}

.ease-in-out {
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

@keyframes enter {
    0% {
        opacity: var(--tw-enter-opacity, 1);
        transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0))
    }
}

@keyframes exit {
    to {
        opacity: var(--tw-exit-opacity, 1);
        transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0))
    }
}

.duration-200 {
    animation-duration: .2s
}

.duration-300 {
    animation-duration: .3s
}

.delay-200 {
    animation-delay: .2s
}

.ease-in-out {
    animation-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.running {
    animation-play-state: running
}

.paused {
    animation-play-state: paused
}

.placeholder\:text-\[\#7FE4C5\]\/60::-moz-placeholder {
    color: rgba(127, 228, 197, .6)
}

.placeholder\:text-\[\#7FE4C5\]\/60::placeholder {
    color: rgba(127, 228, 197, .6)
}

.placeholder\:text-\[\#A16BFB\]\/60::-moz-placeholder {
    color: rgba(161, 107, 251, .6)
}

.placeholder\:text-\[\#A16BFB\]\/60::placeholder {
    color: rgba(161, 107, 251, .6)
}

.placeholder\:text-gray-300::-moz-placeholder {
    --tw-text-opacity: 1;
    color: rgb(209 213 219/var(--tw-text-opacity, 1))
}

.placeholder\:text-gray-300::placeholder {
    --tw-text-opacity: 1;
    color: rgb(209 213 219/var(--tw-text-opacity, 1))
}

.hover\:scale-105:hover {
    --tw-scale-x: 1.05;
    --tw-scale-y: 1.05
}

.hover\:scale-105:hover,
.hover\:scale-110:hover {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.hover\:scale-110:hover {
    --tw-scale-x: 1.1;
    --tw-scale-y: 1.1
}

@keyframes spin {
    to {
        transform: rotate(1turn)
    }
}

.hover\:animate-spin:hover {
    animation: spin 1s linear infinite
}

.hover\:cursor-pointer:hover {
    cursor: pointer
}

.hover\:rounded-full:hover {
    border-radius: 9999px
}

.hover\:border-gray-600:hover {
    --tw-border-opacity: 1;
    border-color: rgb(75 85 99/var(--tw-border-opacity, 1))
}

.hover\:bg-\[\#14b8a6\]:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(20 184 166/var(--tw-bg-opacity, 1))
}

.hover\:bg-\[\#14b8a6\]\/80:hover {
    background-color: rgba(20, 184, 166, .8)
}

.hover\:bg-\[\#22AE83\]:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(34 174 131/var(--tw-bg-opacity, 1))
}

.hover\:bg-\[\#22AE83\]\/80:hover {
    background-color: rgba(34, 174, 131, .8)
}

.hover\:bg-\[\#A16BFB\]:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(161 107 251/var(--tw-bg-opacity, 1))
}

.hover\:bg-\[\#A16BFB\]\/80:hover {
    background-color: rgba(161, 107, 251, .8)
}

.hover\:bg-accent:hover {
    background-color: hsl(var(--accent))
}

.hover\:bg-destructive\/90:hover {
    background-color: hsl(var(--destructive)/.9)
}

.hover\:bg-gray-500\/50:hover {
    background-color: hsla(220, 9%, 46%, .5)
}

.hover\:bg-gray-700:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(55 65 81/var(--tw-bg-opacity, 1))
}

.hover\:bg-green-200:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(187 247 208/var(--tw-bg-opacity, 1))
}

.hover\:bg-orange-700:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(194 65 12/var(--tw-bg-opacity, 1))
}

.hover\:bg-red-700:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(185 28 28/var(--tw-bg-opacity, 1))
}

.hover\:bg-secondary\/80:hover {
    background-color: hsl(var(--secondary)/.8)
}

.hover\:p-0\.5:hover {
    padding: .125rem
}

.hover\:text-\[\#14b8a6\]:hover {
    --tw-text-opacity: 1;
    color: rgb(20 184 166/var(--tw-text-opacity, 1))
}

.hover\:text-\[\#22AE83\]:hover {
    --tw-text-opacity: 1;
    color: rgb(34 174 131/var(--tw-text-opacity, 1))
}

.hover\:text-\[\#A16BFB\]:hover {
    --tw-text-opacity: 1;
    color: rgb(161 107 251/var(--tw-text-opacity, 1))
}

.hover\:text-accent-foreground:hover {
    color: hsl(var(--accent-foreground))
}

.hover\:text-green-400:hover {
    --tw-text-opacity: 1;
    color: rgb(74 222 128/var(--tw-text-opacity, 1))
}

.hover\:text-red-400:hover {
    --tw-text-opacity: 1;
    color: rgb(248 113 113/var(--tw-text-opacity, 1))
}

.hover\:text-red-500:hover {
    --tw-text-opacity: 1;
    color: rgb(239 68 68/var(--tw-text-opacity, 1))
}

.hover\:text-white:hover {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity, 1))
}

.hover\:underline:hover {
    text-decoration-line: underline
}

.hover\:opacity-80:hover {
    opacity: .8
}

.hover\:duration-300:hover {
    transition-duration: .3s
}

.hover\:ease-in-out:hover {
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.hover\:duration-300:hover {
    animation-duration: .3s
}

.hover\:ease-in-out:hover {
    animation-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.focus\:border-none:focus {
    border-style: none
}

.focus\:border-blue-500:focus {
    --tw-border-opacity: 1;
    border-color: rgb(59 130 246/var(--tw-border-opacity, 1))
}

.focus\:border-indigo-500:focus {
    --tw-border-opacity: 1;
    border-color: rgb(99 102 241/var(--tw-border-opacity, 1))
}

.focus\:outline-none:focus {
    outline: 2px solid transparent;
    outline-offset: 2px
}

.focus\:ring-2:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.focus\:ring-blue-500:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(59 130 246/var(--tw-ring-opacity, 1))
}

.focus\:ring-indigo-500:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(99 102 241/var(--tw-ring-opacity, 1))
}

.disabled\:opacity-50:disabled {
    opacity: .5
}

.group:checked .group-checked\:scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.group:checked .group-checked\:border-transparent {
    border-color: transparent
}

.group:checked .group-checked\:bg-\[\#A16BFB\] {
    --tw-bg-opacity: 1;
    background-color: rgb(161 107 251/var(--tw-bg-opacity, 1))
}

.group:hover .group-hover\:scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.group:hover .group-hover\:bg-gray-800 {
    --tw-bg-opacity: 1;
    background-color: rgb(31 41 55/var(--tw-bg-opacity, 1))
}

.peer:checked~.peer-checked\:scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.peer:checked~.peer-checked\:bg-\[\#A16BFB\] {
    --tw-bg-opacity: 1;
    background-color: rgb(161 107 251/var(--tw-bg-opacity, 1))
}

.peer:disabled~.peer-disabled\:cursor-not-allowed {
    cursor: not-allowed
}

.peer:disabled~.peer-disabled\:opacity-70 {
    opacity: .7
}

.dark\:border-\[\#222\]:is(.dark *) {
    --tw-border-opacity: 1;
    border-color: rgb(34 34 34/var(--tw-border-opacity, 1))
}

.dark\:border-\[\#4A4A4A\]:is(.dark *) {
    --tw-border-opacity: 1;
    border-color: rgb(74 74 74/var(--tw-border-opacity, 1))
}

.dark\:border-\[\#8156C9\]:is(.dark *) {
    --tw-border-opacity: 1;
    border-color: rgb(129 86 201/var(--tw-border-opacity, 1))
}

.dark\:border-black:is(.dark *) {
    --tw-border-opacity: 1;
    border-color: rgb(0 0 0/var(--tw-border-opacity, 1))
}

.dark\:border-black\/50:is(.dark *) {
    border-color: rgba(0, 0, 0, .5)
}

.dark\:border-gray-50:is(.dark *) {
    --tw-border-opacity: 1;
    border-color: rgb(249 250 251/var(--tw-border-opacity, 1))
}

.dark\:border-gray-600:is(.dark *) {
    --tw-border-opacity: 1;
    border-color: rgb(75 85 99/var(--tw-border-opacity, 1))
}

.dark\:border-gray-700:is(.dark *) {
    --tw-border-opacity: 1;
    border-color: rgb(55 65 81/var(--tw-border-opacity, 1))
}

.dark\:border-gray-800:is(.dark *) {
    --tw-border-opacity: 1;
    border-color: rgb(31 41 55/var(--tw-border-opacity, 1))
}

.dark\:border-white\/20:is(.dark *) {
    border-color: hsla(0, 0%, 100%, .2)
}

.dark\:border-b-gray-700:is(.dark *) {
    --tw-border-opacity: 1;
    border-bottom-color: rgb(55 65 81/var(--tw-border-opacity, 1))
}

.dark\:bg-\[\#25272D\]:is(.dark *) {
    --tw-bg-opacity: 1;
    background-color: rgb(37 39 45/var(--tw-bg-opacity, 1))
}

.dark\:bg-\[\#6a7c90\]:is(.dark *) {
    --tw-bg-opacity: 1;
    background-color: rgb(106 124 144/var(--tw-bg-opacity, 1))
}

.dark\:bg-\[\#8156C9\]\/30:is(.dark *) {
    background-color: rgba(129, 86, 201, .3)
}

.dark\:bg-black:is(.dark *) {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 0/var(--tw-bg-opacity, 1))
}

.dark\:bg-gray-200\/50:is(.dark *) {
    background-color: rgba(229, 231, 235, .5)
}

.dark\:bg-gray-700:is(.dark *) {
    --tw-bg-opacity: 1;
    background-color: rgb(55 65 81/var(--tw-bg-opacity, 1))
}

.dark\:bg-gray-800:is(.dark *) {
    --tw-bg-opacity: 1;
    background-color: rgb(31 41 55/var(--tw-bg-opacity, 1))
}

.dark\:bg-gray-950:is(.dark *) {
    --tw-bg-opacity: 1;
    background-color: rgb(3 7 18/var(--tw-bg-opacity, 1))
}

.dark\:bg-green-900:is(.dark *) {
    --tw-bg-opacity: 1;
    background-color: rgb(20 83 45/var(--tw-bg-opacity, 1))
}

.dark\:bg-transparent:is(.dark *) {
    background-color: transparent
}

.dark\:bg-white:is(.dark *) {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity, 1))
}

.dark\:text-\[\#292D32\]:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(41 45 50/var(--tw-text-opacity, 1))
}

.dark\:text-\[\#786bcd\]:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(120 107 205/var(--tw-text-opacity, 1))
}

.dark\:text-black:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(0 0 0/var(--tw-text-opacity, 1))
}

.dark\:text-blue-300:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(147 197 253/var(--tw-text-opacity, 1))
}

.dark\:text-gray-100:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(243 244 246/var(--tw-text-opacity, 1))
}

.dark\:text-gray-200:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(229 231 235/var(--tw-text-opacity, 1))
}

.dark\:text-gray-300:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(209 213 219/var(--tw-text-opacity, 1))
}

.dark\:text-gray-400:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(156 163 175/var(--tw-text-opacity, 1))
}

.dark\:text-gray-50:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(249 250 251/var(--tw-text-opacity, 1))
}

.dark\:text-green-200:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(187 247 208/var(--tw-text-opacity, 1))
}

.dark\:text-purple-300:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(216 180 254/var(--tw-text-opacity, 1))
}

.dark\:text-red-500:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(239 68 68/var(--tw-text-opacity, 1))
}

.dark\:text-slate-400:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(148 163 184/var(--tw-text-opacity, 1))
}

.dark\:text-slate-600:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(71 85 105/var(--tw-text-opacity, 1))
}

.dark\:text-white:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity, 1))
}

.dark\:text-white\/40:is(.dark *) {
    color: hsla(0, 0%, 100%, .4)
}

.dark\:text-white\/50:is(.dark *) {
    color: hsla(0, 0%, 100%, .5)
}

.dark\:shadow-white\/10:is(.dark *) {
    --tw-shadow-color: hsla(0, 0%, 100%, .1);
    --tw-shadow: var(--tw-shadow-colored)
}

.dark\:placeholder\:text-white\/80:is(.dark *)::-moz-placeholder {
    color: hsla(0, 0%, 100%, .8)
}

.dark\:placeholder\:text-white\/80:is(.dark *)::placeholder {
    color: hsla(0, 0%, 100%, .8)
}

.dark\:hover\:border-white:hover:is(.dark *) {
    --tw-border-opacity: 1;
    border-color: rgb(255 255 255/var(--tw-border-opacity, 1))
}

.dark\:hover\:bg-gray-700:hover:is(.dark *) {
    --tw-bg-opacity: 1;
    background-color: rgb(55 65 81/var(--tw-bg-opacity, 1))
}

.dark\:hover\:bg-gray-700\/50:hover:is(.dark *) {
    background-color: rgba(55, 65, 81, .5)
}

.dark\:hover\:text-white:hover:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity, 1))
}

.group:hover .dark\:group-hover\:border-white:is(.dark *) {
    --tw-border-opacity: 1;
    border-color: rgb(255 255 255/var(--tw-border-opacity, 1))
}

.group:hover .dark\:group-hover\:bg-\[\#A16BFB\]:is(.dark *) {
    --tw-bg-opacity: 1;
    background-color: rgb(161 107 251/var(--tw-bg-opacity, 1))
}

.group:active .dark\:group-active\:text-white:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity, 1))
}

@media (min-width:640px) {
    .sm\:mb-5 {
        margin-bottom: 1.25rem
    }

    .sm\:mb-6 {
        margin-bottom: 1.5rem
    }

    .sm\:size-5 {
        height: 1.25rem;
        width: 1.25rem
    }

    .sm\:min-h-60 {
        min-height: 15rem
    }

    .sm\:min-w-60 {
        min-width: 15rem
    }

    .sm\:flex-row {
        flex-direction: row
    }

    .sm\:justify-between {
        justify-content: space-between
    }

    .sm\:gap-y-3 {
        row-gap: .75rem
    }

    .sm\:rounded-xl {
        border-radius: .75rem
    }

    .sm\:p-6 {
        padding: 1.5rem
    }

    .sm\:py-2\.5 {
        padding-bottom: .625rem;
        padding-top: .625rem
    }

    .sm\:text-sm {
        font-size: .875rem;
        line-height: 1.25rem
    }
}

@media (min-width:768px) {
    .md\:mb-6 {
        margin-bottom: 1.5rem
    }

    .md\:mb-8 {
        margin-bottom: 2rem
    }

    .md\:mt-0 {
        margin-top: 0
    }

    .md\:min-h-72 {
        min-height: 18rem
    }

    .md\:min-w-72 {
        min-width: 18rem
    }

    .md\:gap-4 {
        gap: 1rem
    }

    .md\:gap-y-4 {
        row-gap: 1rem
    }

    .md\:gap-y-5 {
        row-gap: 1.25rem
    }

    .md\:rounded-2xl {
        border-radius: 1rem
    }

    .md\:p-8 {
        padding: 2rem
    }

    .md\:px-3\.5 {
        padding-left: .875rem;
        padding-right: .875rem
    }

    .md\:py-2 {
        padding-bottom: .5rem;
        padding-top: .5rem
    }

    .md\:py-3 {
        padding-bottom: .75rem;
        padding-top: .75rem
    }

    .md\:text-left {
        text-align: left
    }

    .md\:text-4xl {
        font-size: 2.25rem;
        line-height: 2.5rem
    }

    .md\:text-base {
        font-size: 1rem;
        line-height: 1.5rem
    }

    .md\:text-lg {
        font-size: 1.125rem;
        line-height: 1.75rem
    }
}

@media (min-width:1024px) {
    .lg\:mb-10 {
        margin-bottom: 2.5rem
    }

    .lg\:mb-8 {
        margin-bottom: 2rem
    }

    .lg\:basis-1\/3 {
        flex-basis: 33.333333%
    }

    .lg\:gap-y-6 {
        row-gap: 1.5rem
    }

    .lg\:rounded-3xl {
        border-radius: 1.5rem
    }

    .lg\:p-10 {
        padding: 2.5rem
    }

    .lg\:text-start {
        text-align: start
    }

    .lg\:text-5xl {
        font-size: 3rem;
        line-height: 1
    }
}

@media (min-width:1280px) {
    .xl\:mb-10 {
        margin-bottom: 2.5rem
    }

    .xl\:mb-12 {
        margin-bottom: 3rem
    }

    .xl\:border-l-\[35px\] {
        border-left-width: 35px
    }

    .xl\:border-r-\[35px\] {
        border-right-width: 35px
    }

    .xl\:p-12 {
        padding: 3rem
    }

    .xl\:text-6xl {
        font-size: 3.75rem;
        line-height: 1
    }
}

.\[\&\>svg\]\:h-2\.5>svg {
    height: .625rem
}

.\[\&\>svg\]\:h-3>svg {
    height: .75rem
}

.\[\&\>svg\]\:w-2\.5>svg {
    width: .625rem
}

.\[\&\>svg\]\:w-3>svg {
    width: .75rem
}

.\[\&\>svg\]\:text-muted-foreground>svg {
    color: hsl(var(--muted-foreground))
}

.\[\&_\.recharts-cartesian-axis-tick_text\]\:fill-muted-foreground .recharts-cartesian-axis-tick text {
    fill: hsl(var(--muted-foreground))
}

.\[\&_\.recharts-cartesian-grid_line\]\:stroke-border\/50 .recharts-cartesian-grid line {
    stroke: hsl(var(--border)/.5)
}

.\[\&_\.recharts-curve\.recharts-tooltip-cursor\]\:stroke-border .recharts-curve.recharts-tooltip-cursor {
    stroke: hsl(var(--border))
}

.\[\&_\.recharts-dot\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-dot[stroke="#fff"] {
    stroke: transparent
}

.\[\&_\.recharts-layer\]\:outline-none .recharts-layer {
    outline: 2px solid transparent;
    outline-offset: 2px
}

.\[\&_\.recharts-polar-grid_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-polar-grid [stroke="#ccc"] {
    stroke: hsl(var(--border))
}

.\[\&_\.recharts-radial-bar-background-sector\]\:fill-muted .recharts-radial-bar-background-sector,
.\[\&_\.recharts-rectangle\.recharts-tooltip-cursor\]\:fill-muted .recharts-rectangle.recharts-tooltip-cursor {
    fill: hsl(var(--muted))
}

.\[\&_\.recharts-reference-line-line\]\:stroke-border .recharts-reference-line-line {
    stroke: hsl(var(--border))
}

.\[\&_\.recharts-sector\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-sector[stroke="#fff"] {
    stroke: transparent
}

.\[\&_\.recharts-sector\]\:outline-none .recharts-sector,
.\[\&_\.recharts-surface\]\:outline-none .recharts-surface {
    outline: 2px solid transparent;
    outline-offset: 2px
}
