import { Link } from '@remix-run/react';
import type { MetaFunction, LoaderFunction } from '@remix-run/node';
import { json } from '@remix-run/node';
import {
    ClocBasic,
    ClocProvider,
    ModernCloc,
    ClocLoginDialog,
    ClocRegistrationForm,
    DailyActivityDisplayer,
    WeeklyActivityDisplayer,
    WorkedProjectDisplayer,
    ClocProfileForm,
    ClocTeamCreationForm,
    ClocTeamsViewer,
    ClocTeamMembers,
    ClocPomodoroTimer,
    DailyWorkedTimeDisplayer,
    WeeklyWorkedTimeDisplayer,
    ClocPasswordUpdateForm,
    ClocAccountDeletionForm,
    ClocMemberInvitationForm,
    ClocTeamSetting,
    ClocMemberInvitationFormDialog,
    ClocTeamCreationFormDialog,
    ClocProjectsList,
    ClocTasksList,
    ClocAppsUrlList,
    BasicClocReport,
    ClocThemeToggle
} from '@cloc/atoms/universal';
import React from 'react';
import { cn } from '@cloc/ui';

export const meta: MetaFunction = () => [{ title: 'CLOC Atoms – Component Gallery' }];

export const loader: LoaderFunction = async () => {
    return json({
        ENV: {
            NODE_ENV: process.env.NODE_ENV,
        },
    });
};

type Demo = { title: string; element: React.ReactNode };
type Cat = { name: string; description?: string; demos?: Demo[] };

const categories: Cat[] = [
    {
        name: 'Timers',
        description: 'Timer widgets and progress indicators',
        demos: [
            {
                title: 'ModernCloc – small default',
                element: (
                    <ModernCloc variant="default" size="sm" showProgress={false} expanded={false} draggable={false} resizable={false} />
                )
            },
            {
                title: 'ModernCloc – default with progress',
                element: (
                    <ModernCloc variant="default" size="default" showProgress={true} expanded={false} draggable={false} resizable={false} />
                )
            },
            {
                title: 'ModernCloc – large bordered',
                element: (
                    <ModernCloc variant="bordered" size="lg" showProgress={false} expanded={false} draggable={false} resizable={false} />
                )
            },
            {
                title: 'ModernCloc – expanded default',
                element: (
                    <ModernCloc variant="default" size="default" showProgress={true} expanded={true} draggable={false} resizable={false} />
                )
            },
            {
                title: 'ModernCloc – large bordered expanded',
                element: (
                    <ModernCloc variant="bordered" size="lg" showProgress={true} expanded={true} draggable={false} resizable={false} />
                )
            },
            {
                title: 'ModernCloc – resizable full features',
                element: (
                    <ModernCloc variant="bordered" size="default" showProgress={true} expanded={true} resizable={true} draggable={false} />
                )
            },
            {
                title: 'ClocBasic – default',
                element: (
                    <ClocBasic progress />
                )
            },
            {
                title: 'ClocBasic – thick border',
                element: (
                    <ClocBasic border="thick" progress />
                )
            },
            {
                title: 'ClocBasic – gray rounded',
                element: (
                    <ClocBasic background="secondary" rounded="small" progress />
                )
            },
            {
                title: 'ClocBasic – contained primary',
                element: (
                    <ClocBasic background="primary" color="destructive" progress />
                )
            },
            {
                title: 'ClocBasic – icon + progress',
                element: (
                    <ClocBasic icon progress />
                )
            },
            {
                title: 'ClocPomodoroTimer',
                element: (
                    <ClocPomodoroTimer />
                )
            }
        ]
    },
    {
        name: 'Activity Displayers',
        description: 'Components for displaying work activity and time tracking',
        demos: [
            {
                title: 'DailyActivityDisplayer',
                element: (
                    <DailyActivityDisplayer showProgress={true} className="" />
                )
            },
            {
                title: 'WeeklyActivityDisplayer',
                element: (
                    <WeeklyActivityDisplayer showProgress={true} className="" />
                )
            },
            {
                title: 'WorkedProjectDisplayer',
                element: (
                    <WorkedProjectDisplayer showProgress={true} className="" />
                )
            },
            {
                title: 'DailyWorkedTimeDisplayer',
                element: (
                    <DailyWorkedTimeDisplayer showProgress={true} className="" />
                )
            },
            {
                title: 'WeeklyWorkedTimeDisplayer',
                element: (
                    <WeeklyWorkedTimeDisplayer showProgress={true} className="" />
                )
            }
        ]
    },
    {
        name: 'Authentication & User Management',
        description: 'Components for user authentication and profile management',
        demos: [
            {
                title: 'ClocLoginDialog',
                element: (
                    <ClocLoginDialog trigger="" signupLink="" redirectHandler="" />
                )
            },
            {
                title: 'ClocRegistrationForm',
                element: (
                    <ClocRegistrationForm signInLink="" redirectHandler="" className="" />
                )
            },
            {
                title: 'ClocProfileForm',
                element: (
                    <ClocProfileForm className="" />
                )
            },
            {
                title: 'ClocPasswordUpdateForm',
                element: (
                    <ClocPasswordUpdateForm className="" />
                )
            },
            {
                title: 'ClocAccountDeletionForm',
                element: (
                    <ClocAccountDeletionForm className="" />
                )
            }
        ]
    },
    {
        name: 'Team Management',
        description: 'Components for team creation and management',
        demos: [
            {
                title: 'ClocTeamCreationForm',
                element: (
                    <ClocTeamCreationForm className="" />
                )
            },
            {
                title: 'ClocTeamsViewer',
                element: (
                    <ClocTeamsViewer className="" />
                )
            },
            {
                title: 'ClocTeamMembers',
                element: (
                    <ClocTeamMembers className="" />
                )
            },
            {
                title: 'ClocMemberInvitationForm',
                element: (
                    <ClocMemberInvitationForm className="" />
                )
            },
            {
                title: 'ClocTeamSetting',
                element: (
                    <ClocTeamSetting className="" />
                )
            },
            {
                title: 'ClocMemberInvitationFormDialog',
                element: (
                    <ClocMemberInvitationFormDialog trigger="" className="" />
                )
            },
            {
                title: 'ClocTeamCreationFormDialog',
                element: (
                    <ClocTeamCreationFormDialog trigger="" className="" />
                )
            }
        ]
    },
    {
        name: 'Project & Task Management',
        description: 'Components for managing projects and tasks',
        demos: [
            {
                title: 'ClocProjectsList',
                element: (
                    <ClocProjectsList className="" variant="" size="" />
                )
            },
            {
                title: 'ClocTasksList',
                element: (
                    <ClocTasksList className="" variant="" size="" />
                )
            },
            {
                title: 'ClocAppsUrlList',
                element: (
                    <ClocAppsUrlList className="" variant="" size="" />
                )
            }
        ]
    },
    {
        name: 'UI Components',
        description: 'General UI components and utilities',
        demos: [
            {
                title: 'ClocThemeToggle',
                element: (
                    <ClocThemeToggle className="" size="" />
                )
            },
            {
                title: 'BasicClocReport',
                element: (
                    <BasicClocReport className="" variant="" size="" />
                )
            }
        ]
    }
];

export default function ComponentsGallery() {
    const containerClassName = cn(
        "min-h-screen bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-indigo-100 via-purple-50 to-pink-100",
        "dark:from-gray-950 dark:via-gray-900 dark:to-gray-950",
        "p-8 space-y-8"
    );

    return (
        <ClocProvider>
            <div className={containerClassName}>
                <h1 className="text-3xl font-bold mb-6 bg-gradient-to-r from-violet-500 to-fuchsia-500 dark:from-violet-400 dark:to-fuchsia-400 bg-clip-text text-transparent">CLOC Atoms – Component Gallery</h1>

                <p className="mb-4 text-sm text-gray-600 dark:text-gray-400">
                    Import any component in your Remix route with
                    <code className="px-1 py-0.5 bg-slate-100 dark:bg-slate-800 rounded mx-1 text-gray-800 dark:text-gray-200">import {'{ ComponentName }'} from &apos;@cloc/atoms&apos;</code> or the
                    specific sub-path shown below.
                </p>

                {categories.map((cat) => (
                    <section key={cat.name} className="space-y-4">
                        <header>
                            <h2 className="text-xl font-semibold border-b border-gray-200 dark:border-gray-700 pb-1 text-gray-800 dark:text-gray-200">{cat.name}</h2>
                            {cat.description && <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{cat.description}</p>}
                        </header>
                        {cat.demos ? (
                            <div className="grid gap-6 md:grid-cols-2 grid-cols-1">
                                {cat.demos.map((d) => (
                                    <div key={d.title} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm space-y-2 bg-white/50 dark:bg-gray-800/30 backdrop-blur-sm hover:shadow-md transition-shadow duration-200">
                                        <h3 className="font-medium text-sm text-gray-700 dark:text-gray-300">{d.title}</h3>
                                        {d.element}
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <p className="text-sm text-gray-400 dark:text-gray-500 italic">Component previews will be added here.</p>
                        )}
                    </section>
                ))}

                <div className="mt-12 text-center">
                    <Link to="/" className="text-sm text-gray-600 dark:text-gray-400 hover:text-violet-500 dark:hover:text-violet-400 transition-colors">
                        ← Back to home
                    </Link>
                </div>
            </div>
        </ClocProvider>
    );
}
