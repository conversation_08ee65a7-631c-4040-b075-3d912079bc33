# Clarity Replay API Integration Guide

This guide explains how the enhanced Clarity replay system has been integrated with your real tracking API endpoints, maintaining all production-ready features while adapting to your actual backend.

## 🔗 API Integration Overview

The integration connects the enhanced Clarity replay system with your tracking API at `/api/tracking/route.ts`, providing:

- **Real data loading** from your tracking endpoints
- **Type-safe API communication** with comprehensive error handling
- **Organization/tenant context** support via headers
- **Caching and performance optimization** for API responses
- **Retry logic** for network resilience

## 📁 New File Structure

```
src/app/replay/
├── api/
│   └── tracking-client.ts              # API client for tracking endpoints
├── types/
│   └── api-types.ts                    # TypeScript interfaces for API
├── hooks/
│   ├── useClarityData.ts              # Enhanced with API integration
│   └── useErrorHandler.ts             # Enhanced error categorization
├── components/
│   ├── ClarityTimeline.tsx            # Advanced timeline component
│   └── ClarityAdvancedControls.tsx    # Enhanced controls
├── clarity-replay.tsx                  # Core component (enhanced)
├── ClarityReplayWrapper.tsx           # Next.js wrapper
├── ClarityReplayEnhanced.tsx          # Full-featured component
├── api-integration-example.tsx        # Complete API example
└── example-page.tsx                   # Updated example
```

## 🔧 API Client Integration

### TrackingApiClient Class

The `TrackingApiClient` provides a type-safe interface to your tracking API:

```typescript
import { trackingApiClient } from './api/tracking-client';

// Set organization/tenant context
trackingApiClient.setContext('org-123', 'tenant-456');

// Get all sessions
const response = await trackingApiClient.getSessions();

// Store new session
const storeResponse = await trackingApiClient.storeSession({
  payload: encodedData,
  timestamp: new Date().toISOString()
});
```

### Key Features

- **Automatic header management** for organization-id and tenant-id
- **Request timeout handling** with configurable timeouts
- **Response validation** ensuring API contract compliance
- **Error categorization** for intelligent retry logic
- **Type safety** with comprehensive TypeScript interfaces

## 📊 Enhanced Data Management

### Updated useClarityData Hook

The hook now integrates with your real API endpoints:

```typescript
const {
  // Data
  decodedData,        // Decoded Clarity payloads
  sessions,           // Raw session data from API
  
  // State
  loading,            // Loading state
  error,              // Error messages
  progress,           // Loading progress (0-100)
  metadata,           // Processing metadata
  
  // Actions
  load,               // Load specific session
  loadAllSessions,    // Load all sessions for context
  refetch,            // Refetch current data
  clearCache,         // Clear cached data
  
  // API access
  apiClient           // Direct API client access
} = useClarityData({
  sessionId: 'session-123',
  organizationId: 'org-123',
  tenantId: 'tenant-456',
  employeeId: 'emp-789',
  autoLoad: true,
  enableCaching: true,
  retryAttempts: 3
});
```

### API Context Management

The hook automatically manages API context:

```typescript
// Context is set automatically when options change
useEffect(() => {
  trackingApiClient.setContext(organizationId, tenantId);
}, [organizationId, tenantId]);
```

## 🔄 Data Flow

### 1. Session Loading

```mermaid
graph TD
    A[useClarityData Hook] --> B[trackingApiClient.getSessionsWithContext]
    B --> C[/api/tracking GET]
    C --> D[Filter by employeeId if provided]
    D --> E[Cache sessions]
    E --> F[Update component state]
```

### 2. Session Decoding

```mermaid
graph TD
    A[load(sessionId)] --> B[Find session in loaded data]
    B --> C[decode(session.payload)]
    C --> D[Create IDecodedSessionData]
    D --> E[Cache decoded data]
    E --> F[Update decodedData state]
```

### 3. Error Handling

```mermaid
graph TD
    A[API Error] --> B[categorizeError]
    B --> C{Recoverable?}
    C -->|Yes| D[Retry with backoff]
    C -->|No| E[Show error to user]
    D --> F{Max retries?}
    F -->|No| D
    F -->|Yes| E
```

## 🎯 Usage Examples

### Basic API Integration

```typescript
import { useClarityData } from './hooks/useClarityData';

function MyComponent() {
  const { sessions, loadAllSessions, loading, error } = useClarityData({
    organizationId: 'my-org',
    tenantId: 'my-tenant',
    autoLoad: true
  });

  if (loading) return <div>Loading sessions...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <h2>Sessions ({sessions.length})</h2>
      {sessions.map(session => (
        <div key={session.timestamp}>
          Employee: {session.employeeId}
          Time: {session.timestamp}
        </div>
      ))}
    </div>
  );
}
```

### Complete Replay Integration

```typescript
import ClarityReplayEnhanced from './ClarityReplayEnhanced';

function ReplayPage() {
  return (
    <ClarityReplayEnhanced
      sessionId="session-123"
      organizationId="org-123"
      tenantId="tenant-456"
      autoPlay={false}
      showAdvancedControls={true}
      enableFullscreen={true}
      onReady={() => console.log('Replay ready')}
      onError={(error) => console.error('Error:', error)}
    />
  );
}
```

### Manual API Operations

```typescript
import { trackingApiClient } from './api/tracking-client';

// Store new session
const storeSession = async (sessionData) => {
  const response = await trackingApiClient.storeSessionWithContext(
    {
      payload: JSON.stringify(sessionData),
      timestamp: new Date().toISOString()
    },
    'org-123',
    'tenant-456'
  );

  if (response.success) {
    console.log('Session stored:', response.data);
  } else {
    console.error('Store failed:', response.error);
  }
};
```

## 🔒 Type Safety

### API Response Types

All API responses follow the consistent format from your tracking API:

```typescript
// Success response
interface ITrackingApiSuccessResponse<T> {
  success: true;
  data: T;
}

// Error response
interface ITrackingApiErrorResponse {
  success: false;
  error: string;
  details?: string;
  errorStack?: string;
}
```

### Session Data Types

```typescript
// Base session from API
interface ISession {
  payload: string;
  timestamp: string;
}

// Extended with Cloc context
interface IClocSession extends ISession {
  employeeId: string;
  organizationId: string;
  tenantId: string;
}

// Enhanced for Clarity integration
interface IClaritySessionData {
  id: string;
  session: IClocSession;
  decodedPayload?: Data.DecodedPayload;
  metadata?: {
    duration?: number;
    eventCount?: number;
    lastActivity?: string;
  };
}
```

## 🚨 Error Handling

### Enhanced Error Categorization

The error handler now recognizes API-specific errors:

- **API errors**: Tracking endpoint failures (recoverable)
- **Network errors**: Timeout, connection issues (recoverable)
- **Auth errors**: Unauthorized, forbidden (not recoverable)
- **Session errors**: Session not found (not recoverable)
- **Server errors**: 5xx status codes (recoverable)
- **Client errors**: 4xx status codes (not recoverable)

### Retry Logic

```typescript
// Automatic retry for recoverable errors
const { handleError } = useErrorHandler({
  maxRetries: 3,
  retryDelay: 1000,
  onError: (errorInfo) => {
    console.error('API Error:', errorInfo);
    // Send to error reporting service
  }
});
```

## 🎛️ Configuration

### API Client Configuration

```typescript
// Update API client configuration
trackingApiClient.updateConfig({
  baseUrl: '/api/tracking',
  timeout: 30000,
  headers: {
    'organization-id': 'my-org',
    'tenant-id': 'my-tenant'
  }
});
```

### Hook Configuration

```typescript
const { ... } = useClarityData({
  // API context
  organizationId: 'org-123',
  tenantId: 'tenant-456',
  employeeId: 'emp-789',
  
  // Behavior
  autoLoad: true,
  enableCaching: true,
  retryAttempts: 3,
  retryDelay: 1000
});
```

## 📈 Performance Optimizations

### Intelligent Caching

- **Session caching**: Sessions cached by organization/tenant/employee context
- **Decoded data caching**: Decoded payloads cached by session ID
- **LRU eviction**: Automatic cleanup of old cache entries
- **Cache invalidation**: Manual cache clearing when needed

### Request Optimization

- **Context headers**: Automatic header management for API requests
- **Request deduplication**: Prevents duplicate requests for same data
- **Progress tracking**: Real-time progress feedback for long operations
- **Cancellation**: Request cancellation support for better UX

## 🔄 Migration from Placeholder API

### Before (Placeholder)
```typescript
// Old placeholder API call
const response = await fetch(`/api/clarity-data?sessionId=${sessionId}`);
const encodedPayloads = await response.json();
```

### After (Real API Integration)
```typescript
// New API integration
const response = await trackingApiClient.getSessionsWithContext(
  organizationId,
  tenantId,
  employeeId
);

if (isSuccessResponse(response)) {
  const sessions = response.data;
  // Process sessions...
}
```

## 🎉 Benefits of API Integration

✅ **Real data**: Works with your actual tracking API endpoints  
✅ **Type safety**: Full TypeScript support with API contracts  
✅ **Error resilience**: Comprehensive error handling and retry logic  
✅ **Performance**: Intelligent caching and request optimization  
✅ **Context aware**: Organization/tenant/employee filtering  
✅ **Production ready**: All enhanced features preserved  
✅ **Maintainable**: Clean separation of concerns and modular design  

The enhanced Clarity replay system now seamlessly integrates with your real backend while maintaining all the production-ready features and performance optimizations!
