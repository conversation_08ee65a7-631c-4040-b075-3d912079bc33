'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Data } from 'clarity-decode';
import { Visualizer } from 'clarity-visualize';
import { PauseIcon, PlayIcon, RotateCcw } from 'lucide-react';
import { Button, Input, ThemedButton } from '@cloc/ui';

type ClarityReplayProps = {
	decodedPayloads: Data.DecodedPayload[]; // Array of decoded payloads
	className?: string;
};

export const formatDuration = (ms: number) => {
	const totalSeconds = Math.floor(ms / 1000);
	const hours = Math.floor(totalSeconds / 3600);
	const minutes = Math.floor((totalSeconds % 3600) / 60);
	const seconds = totalSeconds % 60;
	return [
		hours > 0 ? String(hours).padStart(2, '0') : null,
		String(minutes).padStart(2, '0'),
		String(seconds).padStart(2, '0')
	]
		.filter(Boolean)
		.join(':');
};

const ClarityReplay: React.FC<ClarityReplayProps> = ({ decodedPayloads, className }) => {
	const iframeRef = useRef<HTMLIFrameElement>(null);
	const visualizerRef = useRef<Visualizer | null>(null);
	const decodedPayloadsRef = useRef<Data.DecodedPayload[]>([]);
	const [isPlaying, setIsPlaying] = useState(false);
	const [hasLoaded, setHasLoaded] = useState(false);
	const [, setEvents] = useState<Data.DecodedEvent[]>([]);
	const eventsRef = useRef<Data.DecodedEvent[]>([]);
	const frameRef = useRef<number | null>(null);
	const [currentTime, setCurrentTime] = useState(0);
	const [duration, setDuration] = useState(0);
	const wasPlayingRef = useRef(false);
	const [domReady, setDomReady] = useState(false);

	// Prepare and decode session data
	useEffect(() => {
		if (!iframeRef.current) return;

		if (!visualizerRef.current) {
			visualizerRef.current = new Visualizer();
		}
		const visualizer = visualizerRef.current;

		// Decode all payloads and store

		decodedPayloadsRef.current = decodedPayloads;

		// Merge all for initial DOM and events
		const merged = visualizer.merge(decodedPayloads);
		setEvents([...(merged.events as Data.DecodedEvent[])]);
		eventsRef.current = [...(merged.events as Data.DecodedEvent[])];
		setCurrentTime(0);
		setDuration(merged.events.length > 0 ? merged.events[merged.events.length - 1].time : 0);

		// Setup iframe and render initial DOM
		const iframe = iframeRef.current;
		if (iframe) {
			iframe.onload = () => {
				visualizer.setup(iframe.contentWindow as Window, {
					version: 'dev',
					onresize: () => {},
					metadata: undefined,
					mobile: false,
					vNext: true,
					locale: 'en-us',
					onclickMismatch: () => {}
				});
				visualizer.dom(merged.dom);
				setDomReady(true);

				setHasLoaded(true);
			};
			// Force reload iframe to trigger onload
			iframe.srcdoc = '<!DOCTYPE html><html><head></head><body></body></html>';
		}
	}, [decodedPayloads]);

	// Playback loop
	const replayLoop = useCallback(() => {
		if (!isPlaying) return;
		const visualizer = visualizerRef.current;
		if (!visualizer || !domReady) return;

		const events = eventsRef.current;
		// Execute only if there are events to render
		if (events.length > 0) {
			let event = events[0];
			let end = event.time + 16; // 60FPS => 16ms / frame

			let index = 0;
			const missingFields: string[] = [];
			while (event && event.time < end) {
				if (event.time === undefined) {
					missingFields.push(`event at index ${index} is missing 'time'`);
				}
				event = events[++index];
			}
			if (missingFields.length > 0) {
				console.error('Replay error: Missing required fields in events:', missingFields.join('; '));
			}
			const toRender = events.splice(0, index);
			if (toRender.length > 0) {
				setCurrentTime(toRender[toRender.length - 1].time);
			}
			visualizer.render(toRender);
		}
		frameRef.current = requestAnimationFrame(replayLoop);
	}, [isPlaying, domReady]);

	// Start/stop playback based on isPlaying
	useEffect(() => {
		if (isPlaying) {
			frameRef.current = requestAnimationFrame(replayLoop);
		} else if (frameRef.current) {
			cancelAnimationFrame(frameRef.current);
			frameRef.current = null;
		}
		return () => {
			if (frameRef.current) {
				cancelAnimationFrame(frameRef.current);
				frameRef.current = null;
			}
		};
	}, [isPlaying, replayLoop]);

	// Seek to a specific time
	const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
		const seekTime = Number(e.target.value);
		setCurrentTime(seekTime);
		setIsPlaying(false);
		wasPlayingRef.current = isPlaying;

		// Merge all payloads for a fresh DOM and events
		const decodedPayloads = decodedPayloadsRef.current;
		const merged = visualizerRef.current!.merge(decodedPayloads);
		const allEvents = merged.events as Data.DecodedEvent[];
		const seekIndex = allEvents.findIndex((ev) => ev.time > seekTime);
		const eventsToRender = seekIndex === -1 ? allEvents : allEvents.slice(0, seekIndex);

		if (iframeRef.current && visualizerRef.current) {
			visualizerRef.current.dom(merged.dom);
			setDomReady(true);

			visualizerRef.current.render(eventsToRender);
		}
		eventsRef.current = allEvents.slice(seekIndex === -1 ? allEvents.length : seekIndex) as Data.DecodedEvent[];
	};

	// Resume playback if it was playing before seek
	useEffect(() => {
		if (!isPlaying && wasPlayingRef.current) {
			setIsPlaying(true);
			wasPlayingRef.current = false;
		}
	}, [currentTime]);

	// Play
	const handlePlay = () => {
		setIsPlaying(true);
	};

	// Pause
	const handlePause = () => {
		setIsPlaying(false);
	};

	// Replay (reset events and DOM, then play)
	const handleReplay = () => {
		setIsPlaying(false);
		setTimeout(() => {
			const decodedPayloads = decodedPayloadsRef.current;
			const merged = visualizerRef.current!.merge(decodedPayloads);
			eventsRef.current = [...(merged.events as Data.DecodedEvent[])];
			if (iframeRef.current && visualizerRef.current) {
				visualizerRef.current.dom(merged.dom);
				setDomReady(true);
			}
			setCurrentTime(0);
			setIsPlaying(true);
		}, 100);
	};

	return (
		<div
			className={'w-full h-full flex flex-col justify-center items-center bg-black  overflow-hidden ' + className}
		>
			<iframe
				ref={iframeRef}
				scrolling="no"
				id="clarity-replay"
				title="Clarity Session Replay"
				className="w-full  h-full"
			/>
			{/* Controls overlay */}
			<div className=" w-full flex-col dark:bg-black/70 bg-white text-black dark:text-white  px-6 py-4 flex gap-2 z-10">
				{/* Slider and time */}

				<Input
					type="range"
					min={0}
					max={duration}
					value={currentTime}
					onChange={handleSeek}
					className="w-full flex-1 h-2 accent-blue-500 bg-gradient-to-r from-blue-500 to-blue-300 rounded-lg outline-none disabled:opacity-50"
					disabled={!hasLoaded || duration === 0}
				/>

				{/* Controls row */}
				<div className="flex items-center justify-between gap-3 mt-1">
					<div className="flex gap-2 justify-center items-center">
						<ThemedButton
							onClick={isPlaying ? handlePause : handlePlay}
							disabled={!hasLoaded}
							className="p-3  text-white rounded-full transition-colors duration-200 focus:outline-none "
							title={isPlaying ? 'Pause' : 'Play'}
						>
							{isPlaying ? <PauseIcon size={15} /> : <PlayIcon size={15} />}
						</ThemedButton>
						<Button
							onClick={handleReplay}
							disabled={!hasLoaded}
							variant={'outline'}
							className="bg-transparent border-none rounded-full  text-2xl p-3 transition hover:bg-black/10 disabled:opacity-50 disabled:cursor-not-allowed"
							title="Replay"
						>
							<RotateCcw size={20} />
						</Button>
					</div>
					<span className="min-w-[90px] text-sm text-shadow-sm">
						{formatDuration(currentTime)} / {formatDuration(duration)}
					</span>
				</div>
			</div>
		</div>
	);
};

export default ClarityReplay;
