'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Data } from 'clarity-decode';

interface ClarityTimelineProps {
	decodedData: Data.DecodedPayload[];
	currentTime: number;
	duration: number;
	onSeek: (time: number) => void;
	className?: string;
	showEventMarkers?: boolean;
	showHoverPreview?: boolean;
}

interface EventMarker {
	time: number;
	type: string;
	count: number;
}

/**
 * Advanced timeline component with event markers and hover preview
 */
const ClarityTimeline: React.FC<ClarityTimelineProps> = ({
	decodedData,
	currentTime,
	duration,
	onSeek,
	className = '',
	showEventMarkers = true,
	showHoverPreview = true
}) => {
	const [isDragging, setIsDragging] = useState(false);
	const [hoverTime, setHoverTime] = useState<number | null>(null);
	const [eventMarkers, setEventMarkers] = useState<EventMarker[]>([]);
	const timelineRef = useRef<HTMLDivElement>(null);

	// Calculate event markers
	useEffect(() => {
		if (!showEventMarkers || !decodedData.length) return;

		const markers: EventMarker[] = [];
		const timeSlots = Math.min(100, Math.floor(duration / 1000)); // Max 100 markers
		const slotDuration = duration / timeSlots;

		for (let i = 0; i < timeSlots; i++) {
			const slotStart = i * slotDuration;
			const slotEnd = (i + 1) * slotDuration;
			const eventsInSlot: { [key: string]: number } = {};

			decodedData.forEach((payload) => {
				Object.values(payload).forEach((events) => {
					if (Array.isArray(events)) {
						events.forEach((event) => {
							if (event.time >= slotStart && event.time < slotEnd) {
								const eventType = event.event || 'unknown';
								eventsInSlot[eventType] = (eventsInSlot[eventType] || 0) + 1;
							}
						});
					}
				});
			});

			const totalEvents = Object.values(eventsInSlot).reduce((sum, count) => sum + count, 0);
			if (totalEvents > 0) {
				const primaryType = Object.entries(eventsInSlot).reduce((a, b) =>
					eventsInSlot[a[0]] > eventsInSlot[b[0]] ? a : b
				)[0];

				markers.push({
					time: slotStart + slotDuration / 2,
					type: primaryType,
					count: totalEvents
				});
			}
		}

		setEventMarkers(markers);
	}, [decodedData, duration, showEventMarkers]);

	// Handle mouse events
	const handleMouseDown = useCallback((e: React.MouseEvent) => {
		setIsDragging(true);
		handleSeek(e);
	}, []);

	const handleMouseMove = useCallback(
		(e: React.MouseEvent) => {
			if (!timelineRef.current) return;

			const rect = timelineRef.current.getBoundingClientRect();
			const x = e.clientX - rect.left;
			const percentage = Math.max(0, Math.min(1, x / rect.width));
			const time = percentage * duration;

			if (showHoverPreview) {
				setHoverTime(time);
			}

			if (isDragging) {
				handleSeek(e);
			}
		},
		[isDragging, duration, showHoverPreview]
	);

	const handleMouseUp = useCallback(() => {
		setIsDragging(false);
	}, []);

	const handleMouseLeave = useCallback(() => {
		setIsDragging(false);
		setHoverTime(null);
	}, []);

	const handleSeek = useCallback(
		(e: React.MouseEvent) => {
			if (!timelineRef.current) return;

			const rect = timelineRef.current.getBoundingClientRect();
			const x = e.clientX - rect.left;
			const percentage = Math.max(0, Math.min(1, x / rect.width));
			const seekTime = percentage * duration;
			onSeek(seekTime);
		},
		[duration, onSeek]
	);

	// Format time display
	const formatTime = useCallback((time: number) => {
		const seconds = Math.floor(time / 1000);
		const minutes = Math.floor(seconds / 60);
		const remainingSeconds = seconds % 60;
		return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
	}, []);

	// Get event type color
	const getEventTypeColor = useCallback((type: string) => {
		const colors: { [key: string]: string } = {
			click: '#ef4444',
			scroll: '#3b82f6',
			input: '#10b981',
			navigation: '#f59e0b',
			resize: '#8b5cf6',
			unknown: '#6b7280'
		};
		return colors[type] || colors.unknown;
	}, []);

	const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;
	const hoverPercentage = hoverTime && duration > 0 ? (hoverTime / duration) * 100 : null;

	return (
		<div className={`w-full ${className}`}>
			{/* Time display */}
			<div className="flex items-center justify-between text-sm text-gray-600 mb-2">
				<span>{formatTime(currentTime)}</span>
				{showHoverPreview && hoverTime !== null && (
					<span className="text-blue-600">{formatTime(hoverTime)}</span>
				)}
				<span>{formatTime(duration)}</span>
			</div>

			{/* Timeline container */}
			<div className="relative">
				{/* Main timeline */}
				<div
					ref={timelineRef}
					className="relative h-3 bg-gray-200 rounded-full cursor-pointer group"
					onMouseDown={handleMouseDown}
					onMouseMove={handleMouseMove}
					onMouseUp={handleMouseUp}
					onMouseLeave={handleMouseLeave}
				>
					{/* Event markers */}
					{showEventMarkers &&
						eventMarkers.map((marker, index) => {
							const markerPercentage = (marker.time / duration) * 100;
							return (
								<div
									key={index}
									className="absolute top-0 w-1 h-full opacity-60 hover:opacity-100 transition-opacity"
									style={{
										left: `${markerPercentage}%`,
										backgroundColor: getEventTypeColor(marker.type),
										transform: 'translateX(-50%)'
									}}
									title={`${marker.type}: ${marker.count} events at ${formatTime(marker.time)}`}
								/>
							);
						})}

					{/* Progress bar */}
					<div
						className="absolute top-0 left-0 h-full bg-blue-600 rounded-full transition-all duration-100"
						style={{ width: `${progressPercentage}%` }}
					/>

					{/* Hover indicator */}
					{showHoverPreview && hoverPercentage !== null && (
						<div
							className="absolute top-0 w-0.5 h-full bg-blue-400 opacity-75"
							style={{ left: `${hoverPercentage}%` }}
						/>
					)}

					{/* Progress handle */}
					<div
						className="absolute top-1/2 w-5 h-5 bg-blue-600 rounded-full transform -translate-y-1/2 -translate-x-1/2 shadow-md group-hover:scale-110 transition-transform cursor-grab active:cursor-grabbing"
						style={{ left: `${progressPercentage}%` }}
					/>
				</div>

				{/* Event type legend */}
				{showEventMarkers && eventMarkers.length > 0 && (
					<div className="flex flex-wrap gap-2 mt-2 text-xs">
						{Array.from(new Set(eventMarkers.map((m) => m.type))).map((type) => (
							<div key={type} className="flex items-center gap-1">
								<div
									className="w-2 h-2 rounded-full"
									style={{ backgroundColor: getEventTypeColor(type) }}
								/>
								<span className="text-gray-600 capitalize">{type}</span>
							</div>
						))}
					</div>
				)}
			</div>

			{/* Hover preview tooltip */}
			{showHoverPreview && hoverTime !== null && hoverPercentage !== null && timelineRef.current && (
				<div
					className="fixed bottom-full mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded shadow-lg pointer-events-none z-10"
					style={{
						left: `${timelineRef.current.getBoundingClientRect().left + (hoverPercentage / 100) * timelineRef.current.getBoundingClientRect().width}px`,
						transform: 'translateX(-50%)'
					}}
				>
					{formatTime(hoverTime)}
				</div>
			)}
		</div>
	);
};

export default ClarityTimeline;
