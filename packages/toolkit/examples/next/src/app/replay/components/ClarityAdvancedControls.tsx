'use client';

import React, { useState, useRef, useCallback } from 'react';
import { 
	Play, 
	Pause, 
	RotateCcw, 
	Download, 
	Settings, 
	Maximize2, 
	Minimize2,
	Volume2,
	VolumeX,
	SkipBack,
	SkipForward
} from 'lucide-react';
import { Button } from '@cloc/ui';
import { ClarityReplayRef } from '../clarity-replay';
import ClarityTimeline from './ClarityTimeline';
import { Data } from 'clarity-decode';

interface ClarityAdvancedControlsProps {
	replayRef: React.RefObject<ClarityReplayRef>;
	decodedData: Data.DecodedPayload[];
	isPlaying: boolean;
	currentTime: number;
	duration: number;
	playbackSpeed: number;
	isFullscreen: boolean;
	onPlay: () => void;
	onPause: () => void;
	onReset: () => void;
	onSeek: (time: number) => void;
	onSpeedChange: (speed: number) => void;
	onFullscreenToggle: () => void;
	onExport?: () => void;
	className?: string;
}

/**
 * Advanced controls component with timeline, speed control, and export functionality
 */
const ClarityAdvancedControls: React.FC<ClarityAdvancedControlsProps> = ({
	replayRef,
	decodedData,
	isPlaying,
	currentTime,
	duration,
	playbackSpeed,
	isFullscreen,
	onPlay,
	onPause,
	onReset,
	onSeek,
	onSpeedChange,
	onFullscreenToggle,
	onExport,
	className = ''
}) => {
	const [showSpeedMenu, setShowSpeedMenu] = useState(false);
	const [showSettings, setShowSettings] = useState(false);
	const [volume, setVolume] = useState(1);
	const [isMuted, setIsMuted] = useState(false);
	const speedMenuRef = useRef<HTMLDivElement>(null);

	const speedOptions = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 2, 3];

	// Handle speed change
	const handleSpeedChange = useCallback((speed: number) => {
		onSpeedChange(speed);
		setShowSpeedMenu(false);
	}, [onSpeedChange]);

	// Handle skip forward/backward
	const handleSkip = useCallback((seconds: number) => {
		const newTime = Math.max(0, Math.min(duration, currentTime + seconds * 1000));
		onSeek(newTime);
	}, [currentTime, duration, onSeek]);

	// Handle export
	const handleExport = useCallback(() => {
		if (onExport) {
			onExport();
		} else {
			// Default export functionality
			const visualizer = replayRef.current?.getVisualizer();
			if (visualizer && decodedData) {
				const merged = visualizer.merge(decodedData);
				const blob = new Blob([JSON.stringify(merged, null, 2)], {
					type: 'application/json',
				});
				const url = URL.createObjectURL(blob);
				const a = document.createElement('a');
				a.href = url;
				a.download = `clarity-session-${Date.now()}.json`;
				a.click();
				URL.revokeObjectURL(url);
			}
		}
	}, [onExport, replayRef, decodedData]);

	// Handle volume change
	const handleVolumeChange = useCallback((newVolume: number) => {
		setVolume(newVolume);
		setIsMuted(newVolume === 0);
	}, []);

	// Toggle mute
	const toggleMute = useCallback(() => {
		setIsMuted(!isMuted);
	}, [isMuted]);

	// Format time for display
	const formatTime = useCallback((time: number) => {
		const totalSeconds = Math.floor(time / 1000);
		const hours = Math.floor(totalSeconds / 3600);
		const minutes = Math.floor((totalSeconds % 3600) / 60);
		const seconds = totalSeconds % 60;
		
		if (hours > 0) {
			return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
		}
		return `${minutes}:${seconds.toString().padStart(2, '0')}`;
	}, []);

	return (
		<div className={`bg-white border-t border-gray-200 ${className}`}>
			{/* Timeline */}
			<div className="px-6 py-4">
				<ClarityTimeline
					decodedData={decodedData}
					currentTime={currentTime}
					duration={duration}
					onSeek={onSeek}
					showEventMarkers={true}
					showHoverPreview={true}
				/>
			</div>

			{/* Main Controls */}
			<div className="flex items-center justify-between px-6 py-4 bg-gray-50">
				{/* Left Controls */}
				<div className="flex items-center space-x-3">
					{/* Play/Pause */}
					<Button
						onClick={isPlaying ? onPause : onPlay}
						className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
					>
						{isPlaying ? <Pause size={16} /> : <Play size={16} />}
						<span className="font-medium">{isPlaying ? 'Pause' : 'Play'}</span>
					</Button>

					{/* Reset */}
					<Button
						onClick={onReset}
						variant="outline"
						className="flex items-center space-x-2 px-4 py-2"
					>
						<RotateCcw size={16} />
						<span className="font-medium">Reset</span>
					</Button>

					{/* Skip Controls */}
					<div className="flex items-center space-x-1">
						<Button
							onClick={() => handleSkip(-10)}
							variant="ghost"
							size="sm"
							className="p-2"
							title="Skip back 10 seconds"
						>
							<SkipBack size={16} />
						</Button>
						<Button
							onClick={() => handleSkip(10)}
							variant="ghost"
							size="sm"
							className="p-2"
							title="Skip forward 10 seconds"
						>
							<SkipForward size={16} />
						</Button>
					</div>

					{/* Time Display */}
					<div className="text-sm text-gray-600 font-mono">
						{formatTime(currentTime)} / {formatTime(duration)}
					</div>
				</div>

				{/* Right Controls */}
				<div className="flex items-center space-x-3">
					{/* Volume Control */}
					<div className="flex items-center space-x-2">
						<Button
							onClick={toggleMute}
							variant="ghost"
							size="sm"
							className="p-2"
						>
							{isMuted || volume === 0 ? <VolumeX size={16} /> : <Volume2 size={16} />}
						</Button>
						<input
							type="range"
							min="0"
							max="1"
							step="0.1"
							value={isMuted ? 0 : volume}
							onChange={(e) => handleVolumeChange(Number(e.target.value))}
							className="w-16 h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer"
						/>
					</div>

					{/* Speed Control */}
					<div className="relative" ref={speedMenuRef}>
						<Button
							onClick={() => setShowSpeedMenu(!showSpeedMenu)}
							variant="ghost"
							className="flex items-center space-x-1 px-3 py-2 text-sm"
						>
							<Settings size={14} />
							<span>{playbackSpeed}x</span>
						</Button>

						{showSpeedMenu && (
							<div className="absolute bottom-full right-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg py-1 min-w-[80px] z-10">
								{speedOptions.map((speed) => (
									<button
										key={speed}
										onClick={() => handleSpeedChange(speed)}
										className={`w-full px-3 py-2 text-sm text-left hover:bg-gray-50 transition-colors ${
											speed === playbackSpeed
												? 'bg-blue-50 text-blue-600 font-medium'
												: 'text-gray-700'
										}`}
									>
										{speed}x
									</button>
								))}
							</div>
						)}
					</div>

					{/* Export */}
					<Button
						onClick={handleExport}
						variant="ghost"
						size="sm"
						className="p-2"
						title="Export session data"
					>
						<Download size={16} />
					</Button>

					{/* Fullscreen */}
					<Button
						onClick={onFullscreenToggle}
						variant="ghost"
						size="sm"
						className="p-2"
						title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
					>
						{isFullscreen ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
					</Button>

					{/* Settings */}
					<Button
						onClick={() => setShowSettings(!showSettings)}
						variant="ghost"
						size="sm"
						className="p-2"
						title="Settings"
					>
						<Settings size={16} />
					</Button>
				</div>
			</div>

			{/* Settings Panel */}
			{showSettings && (
				<div className="border-t border-gray-200 bg-gray-50 px-6 py-4">
					<div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
						<div>
							<label className="block text-gray-700 font-medium mb-1">
								Playback Quality
							</label>
							<select className="w-full px-2 py-1 border border-gray-300 rounded text-sm">
								<option value="auto">Auto</option>
								<option value="high">High</option>
								<option value="medium">Medium</option>
								<option value="low">Low</option>
							</select>
						</div>
						<div>
							<label className="block text-gray-700 font-medium mb-1">
								Event Markers
							</label>
							<label className="flex items-center">
								<input type="checkbox" defaultChecked className="mr-2" />
								<span>Show event markers</span>
							</label>
						</div>
						<div>
							<label className="block text-gray-700 font-medium mb-1">
								Debug Mode
							</label>
							<label className="flex items-center">
								<input type="checkbox" className="mr-2" />
								<span>Enable debug overlay</span>
							</label>
						</div>
					</div>
				</div>
			)}

			{/* Click outside to close menus */}
			{(showSpeedMenu || showSettings) && (
				<div
					className="fixed inset-0 z-0"
					onClick={() => {
						setShowSpeedMenu(false);
						setShowSettings(false);
					}}
				/>
			)}
		</div>
	);
};

export default ClarityAdvancedControls;
