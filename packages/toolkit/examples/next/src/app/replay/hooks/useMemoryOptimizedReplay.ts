'use client';

import { useEffect, useRef, useCallback, useMemo } from 'react';
import { Data } from 'clarity-decode';

interface UseMemoryOptimizedReplayOptions {
	maxCacheSize?: number;
	cleanupInterval?: number;
	enablePerformanceMonitoring?: boolean;
}

interface PerformanceMetrics {
	memoryUsage: number;
	renderTime: number;
	eventCount: number;
	lastCleanup: number;
}

/**
 * Custom hook for memory-optimized Clarity replay management
 * Implements caching, cleanup, and performance monitoring
 */
export const useMemoryOptimizedReplay = (
	decodedData: Data.DecodedPayload[],
	options: UseMemoryOptimizedReplayOptions = {}
) => {
	const {
		maxCacheSize = 100,
		cleanupInterval = 30000, // 30 seconds
		enablePerformanceMonitoring = process.env.NODE_ENV === 'development'
	} = options;

	// Cache for processed data
	const cacheRef = useRef(new Map<string, any>());
	const cleanupTimerRef = useRef<NodeJS.Timeout>();
	const performanceRef = useRef<PerformanceMetrics>({
		memoryUsage: 0,
		renderTime: 0,
		eventCount: 0,
		lastCleanup: Date.now()
	});

	// Memoized data processing
	const processedData = useMemo(() => {
		if (!decodedData.length) return null;

		const startTime = performance.now();
		
		// Create a cache key based on data content
		const cacheKey = decodedData.map(d => d.envelope?.sessionId || '').join('-');
		
		// Check cache first
		if (cacheRef.current.has(cacheKey)) {
			return cacheRef.current.get(cacheKey);
		}

		// Process data
		const allEvents: Data.DecodedEvent[] = [];
		let totalDuration = 0;

		decodedData.forEach(payload => {
			Object.values(payload).forEach(events => {
				if (Array.isArray(events)) {
					events.forEach(event => {
						if (event.time !== undefined) {
							allEvents.push(event);
							totalDuration = Math.max(totalDuration, event.time);
						}
					});
				}
			});
		});

		// Sort events by time
		allEvents.sort((a, b) => a.time - b.time);

		const processed = {
			events: allEvents,
			duration: totalDuration,
			eventCount: allEvents.length,
			cacheKey
		};

		// Cache the processed data
		cacheRef.current.set(cacheKey, processed);

		// Update performance metrics
		if (enablePerformanceMonitoring) {
			const endTime = performance.now();
			performanceRef.current = {
				...performanceRef.current,
				renderTime: endTime - startTime,
				eventCount: allEvents.length
			};
		}

		return processed;
	}, [decodedData, enablePerformanceMonitoring]);

	// Memory cleanup function
	const cleanup = useCallback(() => {
		const cache = cacheRef.current;
		const now = Date.now();

		if (cache.size > maxCacheSize) {
			// Remove oldest entries
			const entries = Array.from(cache.entries());
			const toDelete = entries.slice(0, entries.length - maxCacheSize);
			toDelete.forEach(([key]) => cache.delete(key));
		}

		// Update performance metrics
		if (enablePerformanceMonitoring) {
			performanceRef.current.lastCleanup = now;
			
			// Estimate memory usage (rough approximation)
			if ('memory' in performance) {
				performanceRef.current.memoryUsage = (performance as any).memory.usedJSHeapSize;
			}
		}

		console.log(`Clarity cache cleanup: ${cache.size} entries remaining`);
	}, [maxCacheSize, enablePerformanceMonitoring]);

	// Set up periodic cleanup
	useEffect(() => {
		cleanupTimerRef.current = setInterval(cleanup, cleanupInterval);

		return () => {
			if (cleanupTimerRef.current) {
				clearInterval(cleanupTimerRef.current);
			}
		};
	}, [cleanup, cleanupInterval]);

	// Cleanup on unmount
	useEffect(() => {
		return () => {
			cacheRef.current.clear();
			if (cleanupTimerRef.current) {
				clearInterval(cleanupTimerRef.current);
			}
		};
	}, []);

	// Cache management functions
	const getCachedData = useCallback((key: string) => {
		return cacheRef.current.get(key);
	}, []);

	const setCachedData = useCallback(
		(key: string, data: any) => {
			cacheRef.current.set(key, data);
			
			// Trigger cleanup if cache is getting large
			if (cacheRef.current.size > maxCacheSize) {
				cleanup();
			}
		},
		[cleanup, maxCacheSize]
	);

	const clearCache = useCallback(() => {
		cacheRef.current.clear();
	}, []);

	// Performance monitoring
	const getPerformanceMetrics = useCallback(() => {
		return enablePerformanceMonitoring ? { ...performanceRef.current } : null;
	}, [enablePerformanceMonitoring]);

	// Force cleanup
	const forceCleanup = useCallback(() => {
		cleanup();
	}, [cleanup]);

	return {
		processedData,
		getCachedData,
		setCachedData,
		clearCache,
		forceCleanup,
		getPerformanceMetrics,
		cacheSize: cacheRef.current.size
	};
};
