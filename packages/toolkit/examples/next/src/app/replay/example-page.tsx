'use client';

import React, { useRef, useState } from 'react';
import { Button } from '@cloc/ui';
import ClarityReplayWrapper, { ClarityReplayRef } from './ClarityReplayWrapper';
import { useClarityData } from './hooks/useClarityData';

interface ExamplePageProps {
	sessionId?: string;
	organizationId?: string;
	tenantId?: string;
	employeeId?: string;
}

/**
 * Example page component demonstrating the enhanced Clarity replay integration
 * Shows best practices for Next.js applications
 */
const ExamplePage: React.FC<ExamplePageProps> = ({
	sessionId: initialSessionId,
	organizationId: initialOrgId,
	tenantId: initialTenantId,
	employeeId: initialEmployeeId
}) => {
	const [sessionId, setSessionId] = useState(initialSessionId || '');
	const [organizationId, setOrganizationId] = useState(initialOrgId || '');
	const [tenantId, setTenantId] = useState(initialTenantId || '');
	const [employeeId, setEmployeeId] = useState(initialEmployeeId || '');
	const [playbackSpeed, setPlaybackSpeed] = useState(1);
	const [showControls, setShowControls] = useState(true);
	const replayRef = useRef<ClarityReplayRef>(null);

	// Use the enhanced custom hook for data management with API integration
	const {
		decodedData,
		sessions,
		loading,
		error,
		progress,
		hasData,
		hasSessions,
		metadata,
		load,
		loadAllSessions,
		refetch,
		cancel,
		clearCache,
		getCacheInfo,
		apiClient
	} = useClarityData({
		sessionId: initialSessionId,
		organizationId,
		tenantId,
		employeeId,
		autoLoad: !!initialSessionId,
		enableCaching: true,
		retryAttempts: 3,
		retryDelay: 1000
	});

	// Handle session ID input
	const handleSessionIdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setSessionId(e.target.value);
	};

	// Load session data
	const handleLoadSession = async () => {
		if (sessionId.trim()) {
			await load(sessionId.trim());
		}
	};

	// Playback controls
	const handlePlay = () => {
		replayRef.current?.play();
	};

	const handlePause = () => {
		replayRef.current?.pause();
	};

	const handleReset = () => {
		replayRef.current?.reset();
	};

	const handleSeek = (time: number) => {
		replayRef.current?.seek(time);
	};

	// Speed control
	const handleSpeedChange = (speed: number) => {
		setPlaybackSpeed(speed);
	};

	// Cache management
	const handleClearCache = () => {
		clearCache();
		console.log('Cache cleared');
	};

	// Error handler
	const handleReplayError = (error: Error) => {
		console.error('Replay error:', error);
	};

	// Ready handler
	const handleReplayReady = () => {
		console.log('Replay is ready');
	};

	// Time update handler
	const handleTimeUpdate = (currentTime: number) => {
		// You could update a progress bar or time display here
		console.log('Current time:', currentTime);
	};

	// Play state change handler
	const handlePlayStateChange = (isPlaying: boolean) => {
		console.log('Playing state:', isPlaying);
	};

	const cacheInfo = getCacheInfo();

	return (
		<div className="min-h-screen bg-gray-50 p-4">
			<div className="max-w-7xl mx-auto">
				{/* Header */}
				<div className="mb-8">
					<h1 className="text-3xl font-bold text-gray-900 mb-2">Clarity Session Replay</h1>
					<p className="text-gray-600">
						Enhanced integration with Next.js optimizations, error handling, and performance monitoring
					</p>
				</div>

				{/* Session Input */}
				<div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
					<h2 className="text-lg font-semibold mb-4">Load Session</h2>
					<div className="flex gap-4 items-end">
						<div className="flex-1">
							<label htmlFor="sessionId" className="block text-sm font-medium text-gray-700 mb-2">
								Session ID
							</label>
							<input
								id="sessionId"
								type="text"
								value={sessionId}
								onChange={handleSessionIdChange}
								placeholder="Enter session ID..."
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
							/>
						</div>
						<Button
							onClick={handleLoadSession}
							disabled={!sessionId.trim() || loading}
							className="px-6 py-2"
						>
							{loading ? 'Loading...' : 'Load Session'}
						</Button>
						{loading && (
							<Button onClick={cancel} variant="outline" className="px-4 py-2">
								Cancel
							</Button>
						)}
					</div>

					{/* Progress bar */}
					{loading && (
						<div className="mt-4">
							<div className="flex justify-between text-sm text-gray-600 mb-1">
								<span>Loading session data...</span>
								<span>{Math.round(progress)}%</span>
							</div>
							<div className="w-full bg-gray-200 rounded-full h-2">
								<div
									className="bg-blue-600 h-2 rounded-full transition-all duration-300"
									style={{ width: `${progress}%` }}
								/>
							</div>
						</div>
					)}

					{/* Error display */}
					{error && (
						<div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
							<div className="flex justify-between items-start">
								<div>
									<h3 className="text-sm font-medium text-red-800">Error loading session</h3>
									<p className="text-sm text-red-600 mt-1">{error}</p>
								</div>
								<Button
									onClick={refetch}
									variant="outline"
									size="sm"
									className="text-red-600 border-red-300"
								>
									Retry
								</Button>
							</div>
						</div>
					)}
				</div>

				{/* Controls */}
				{hasData && (
					<div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
						<h2 className="text-lg font-semibold mb-4">Playback Controls</h2>
						<div className="flex flex-wrap gap-4 items-center">
							<Button onClick={handlePlay} size="sm">
								Play
							</Button>
							<Button onClick={handlePause} size="sm" variant="outline">
								Pause
							</Button>
							<Button onClick={handleReset} size="sm" variant="outline">
								Reset
							</Button>

							<div className="flex items-center gap-2">
								<label className="text-sm font-medium text-gray-700">Speed:</label>
								<select
									value={playbackSpeed}
									onChange={(e) => handleSpeedChange(Number(e.target.value))}
									className="px-2 py-1 border border-gray-300 rounded text-sm"
								>
									<option value={0.25}>0.25x</option>
									<option value={0.5}>0.5x</option>
									<option value={1}>1x</option>
									<option value={1.5}>1.5x</option>
									<option value={2}>2x</option>
								</select>
							</div>

							<label className="flex items-center gap-2">
								<input
									type="checkbox"
									checked={showControls}
									onChange={(e) => setShowControls(e.target.checked)}
									className="rounded"
								/>
								<span className="text-sm text-gray-700">Show built-in controls</span>
							</label>
						</div>
					</div>
				)}

				{/* Debug Info */}
				{process.env.NODE_ENV === 'development' && (
					<div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
						<h2 className="text-lg font-semibold mb-4">Debug Information</h2>
						<div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
							<div>
								<strong>Cache:</strong> {cacheInfo.size} sessions
							</div>
							<div>
								<strong>Data:</strong> {decodedData.length} payloads
							</div>
							<div>
								<strong>Speed:</strong> {playbackSpeed}x
							</div>
						</div>
						<Button onClick={handleClearCache} variant="outline" size="sm" className="mt-4">
							Clear Cache
						</Button>
					</div>
				)}

				{/* Replay Component */}
				{hasData && (
					<div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
						<div style={{ height: 'calc(100vh - 400px)', minHeight: '600px' }}>
							<ClarityReplayWrapper
								ref={replayRef}
								decodedPayloads={decodedData}
								autoPlay={false}
								playbackSpeed={playbackSpeed}
								onReady={handleReplayReady}
								onError={handleReplayError}
								onTimeUpdate={handleTimeUpdate}
								onPlayStateChange={handlePlayStateChange}
								className="h-full"
								enableErrorBoundary={true}
							/>
						</div>
					</div>
				)}

				{/* Empty state */}
				{!hasData && !loading && !error && (
					<div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
						<div className="text-gray-400 mb-4">
							<svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={1}
									d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
								/>
							</svg>
						</div>
						<h3 className="text-lg font-medium text-gray-900 mb-2">No Session Loaded</h3>
						<p className="text-gray-600">Enter a session ID above to load and view the replay.</p>
					</div>
				)}
			</div>
		</div>
	);
};

export default ExamplePage;
