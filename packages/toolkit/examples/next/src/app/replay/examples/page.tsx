'use client';

import React, { useState } from 'react';
import { trackingApiClient } from '../api/tracking-client';
import { IClocSession, isSuccessResponse } from '../types/api-types';

/**
 * Example component demonstrating how to use the new filtered GET endpoint
 * for retrieving tracking sessions by organization, employee IDs, and date range
 */
export default function FilteredSessionsExample() {
	const [sessions, setSessions] = useState<IClocSession[]>([]);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [formData, setFormData] = useState({
		organizationId: 'org-456',
		tenantId: 'tenant-789',
		bearerToken: 'your-bearer-token-here',
		from: '2024-01-01T00:00:00.000Z',
		to: '2024-01-31T23:59:59.999Z',
		employeeIds: 'emp-123,emp-456' // Comma-separated for UI
	});

	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
		const { name, value } = e.target;
		setFormData(prev => ({
			...prev,
			[name]: value
		}));
	};

	const fetchFilteredSessions = async () => {
		setLoading(true);
		setError(null);

		try {
			// Convert comma-separated employee IDs to array
			const employeeIdsArray = formData.employeeIds
				.split(',')
				.map(id => id.trim())
				.filter(id => id.length > 0);

			if (employeeIdsArray.length === 0) {
				throw new Error('At least one employee ID is required');
			}

			// Validate date format
			const fromDate = new Date(formData.from);
			const toDate = new Date(formData.to);

			if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {
				throw new Error('Invalid date format. Please use ISO 8601 format (e.g., 2024-01-01T00:00:00.000Z)');
			}

			if (fromDate >= toDate) {
				throw new Error('From date must be earlier than to date');
			}

			// Call the new filtered sessions endpoint
			const response = await trackingApiClient.getFilteredSessions(
				formData.from,
				formData.to,
				employeeIdsArray,
				formData.organizationId,
				formData.tenantId,
				formData.bearerToken
			);

			if (isSuccessResponse(response)) {
				setSessions(response.data);
				setError(null);
			} else {
				setError(response.error || 'Failed to fetch sessions');
				setSessions([]);
			}
		} catch (err) {
			setError(err instanceof Error ? err.message : 'An unexpected error occurred');
			setSessions([]);
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="max-w-4xl mx-auto p-6 space-y-6">
			<div className="bg-white shadow-md rounded-lg p-6">
				<h1 className="text-2xl font-bold text-gray-900 mb-6">
					Filtered Sessions Example
				</h1>
				
				<p className="text-gray-600 mb-6">
					This example demonstrates how to use the new GET endpoint to retrieve tracking sessions 
					filtered by organization ID, employee IDs, and date range.
				</p>

				<div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
					<div>
						<label htmlFor="organizationId" className="block text-sm font-medium text-gray-700 mb-2">
							Organization ID
						</label>
						<input
							type="text"
							id="organizationId"
							name="organizationId"
							value={formData.organizationId}
							onChange={handleInputChange}
							className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							placeholder="org-456"
						/>
					</div>

					<div>
						<label htmlFor="tenantId" className="block text-sm font-medium text-gray-700 mb-2">
							Tenant ID
						</label>
						<input
							type="text"
							id="tenantId"
							name="tenantId"
							value={formData.tenantId}
							onChange={handleInputChange}
							className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							placeholder="tenant-789"
						/>
					</div>

					<div>
						<label htmlFor="bearerToken" className="block text-sm font-medium text-gray-700 mb-2">
							Bearer Token
						</label>
						<input
							type="password"
							id="bearerToken"
							name="bearerToken"
							value={formData.bearerToken}
							onChange={handleInputChange}
							className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							placeholder="your-bearer-token-here"
						/>
					</div>

					<div>
						<label htmlFor="employeeIds" className="block text-sm font-medium text-gray-700 mb-2">
							Employee IDs (comma-separated)
						</label>
						<input
							type="text"
							id="employeeIds"
							name="employeeIds"
							value={formData.employeeIds}
							onChange={handleInputChange}
							className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							placeholder="emp-123,emp-456"
						/>
					</div>

					<div>
						<label htmlFor="from" className="block text-sm font-medium text-gray-700 mb-2">
							From Date (ISO 8601)
						</label>
						<input
							type="text"
							id="from"
							name="from"
							value={formData.from}
							onChange={handleInputChange}
							className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							placeholder="2024-01-01T00:00:00.000Z"
						/>
					</div>

					<div>
						<label htmlFor="to" className="block text-sm font-medium text-gray-700 mb-2">
							To Date (ISO 8601)
						</label>
						<input
							type="text"
							id="to"
							name="to"
							value={formData.to}
							onChange={handleInputChange}
							className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							placeholder="2024-01-31T23:59:59.999Z"
						/>
					</div>
				</div>

				<button
					onClick={fetchFilteredSessions}
					disabled={loading}
					className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
				>
					{loading ? 'Fetching Sessions...' : 'Fetch Filtered Sessions'}
				</button>
			</div>

			{error && (
				<div className="bg-red-50 border border-red-200 rounded-lg p-4">
					<h3 className="text-red-800 font-medium">Error</h3>
					<p className="text-red-600 mt-1">{error}</p>
				</div>
			)}

			<div className="bg-white shadow-md rounded-lg p-6">
				<h2 className="text-xl font-semibold text-gray-900 mb-4">
					Results ({sessions.length} sessions)
				</h2>
				
				{sessions.length === 0 ? (
					<p className="text-gray-500">No sessions found matching the criteria.</p>
				) : (
					<div className="space-y-4">
						{sessions.map((session, index) => (
							<div key={session.sessionId} className="border border-gray-200 rounded-lg p-4">
								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<div>
										<h3 className="font-medium text-gray-900">Session #{index + 1}</h3>
										<p className="text-sm text-gray-600">ID: {session.sessionId}</p>
										<p className="text-sm text-gray-600">Employee: {session.employeeId}</p>
									</div>
									<div>
										<p className="text-sm text-gray-600">Created: {new Date(session.createdAt).toLocaleString()}</p>
										<p className="text-sm text-gray-600">Updated: {new Date(session.updatedAt).toLocaleString()}</p>
										<p className="text-sm text-gray-600">Payloads: {session.payloads.length}</p>
									</div>
								</div>
							</div>
						))}
					</div>
				)}
			</div>

			<div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
				<h3 className="text-lg font-medium text-gray-900 mb-2">API Endpoint Details</h3>
				<div className="text-sm text-gray-600 space-y-1">
					<p><strong>Method:</strong> GET</p>
					<p><strong>Path:</strong> /api/tracking</p>
					<p><strong>Required Headers:</strong></p>
					<ul className="list-disc list-inside ml-4 space-y-1">
						<li>Authorization: Bearer &lt;token&gt;</li>
						<li>organization-id: &lt;organizationId&gt;</li>
						<li>tenant-id: &lt;tenantId&gt;</li>
						<li>Accept: application/json</li>
					</ul>
					<p><strong>Required Query Parameters:</strong></p>
					<ul className="list-disc list-inside ml-4 space-y-1">
						<li>from: ISO 8601 date string</li>
						<li>to: ISO 8601 date string</li>
						<li>employeeIds: JSON array of employee ID strings</li>
					</ul>
				</div>
			</div>
		</div>
	);
}
