'use client';

import React, { Component, ReactNode } from 'react';
import { Button } from '@cloc/ui';

interface Props {
	children: ReactNode;
	fallback?: ReactNode;
	onError?: (error: Error, errorInfo: any) => void;
}

interface State {
	hasError: boolean;
	error?: Error;
	errorInfo?: any;
}

/**
 * Error boundary component specifically designed for Clarity replay components
 * Provides graceful error handling and recovery options
 */
class ClarityErrorBoundary extends Component<Props, State> {
	constructor(props: Props) {
		super(props);
		this.state = { hasError: false };
	}

	static getDerivedStateFromError(error: Error): State {
		// Update state so the next render will show the fallback UI
		return { hasError: true, error };
	}

	componentDidCatch(error: Error, errorInfo: any) {
		console.error('Clarity replay error boundary caught an error:', error, errorInfo);
		
		// Store error info for debugging
		this.setState({ errorInfo });
		
		// Call optional error handler
		this.props.onError?.(error, errorInfo);
		
		// You could also log the error to an error reporting service here
		// Example: logErrorToService(error, errorInfo);
	}

	handleRetry = () => {
		this.setState({ hasError: false, error: undefined, errorInfo: undefined });
	};

	render() {
		if (this.state.hasError) {
			// Custom fallback UI
			if (this.props.fallback) {
				return this.props.fallback;
			}

			// Default error UI
			return (
				<div className="flex flex-col items-center justify-center h-96 bg-red-50 border border-red-200 rounded-lg p-6">
					<div className="text-center max-w-md">
						<div className="text-red-600 mb-4">
							<svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
							</svg>
						</div>
						
						<h3 className="text-lg font-semibold text-red-800 mb-2">
							Session Replay Failed to Load
						</h3>
						
						<p className="text-red-600 mb-4 text-sm">
							{this.state.error?.message || 'An unexpected error occurred while loading the session replay.'}
						</p>
						
						<div className="space-y-2">
							<Button 
								onClick={this.handleRetry}
								className="w-full bg-red-600 hover:bg-red-700 text-white"
							>
								Try Again
							</Button>
							
							{process.env.NODE_ENV === 'development' && this.state.errorInfo && (
								<details className="mt-4 text-left">
									<summary className="cursor-pointer text-sm text-red-600 hover:text-red-800">
										Show Error Details (Development)
									</summary>
									<pre className="mt-2 text-xs bg-red-100 p-2 rounded overflow-auto max-h-32">
										{this.state.error?.stack}
										{'\n\nComponent Stack:'}
										{this.state.errorInfo.componentStack}
									</pre>
								</details>
							)}
						</div>
					</div>
				</div>
			);
		}

		return this.props.children;
	}
}

export default ClarityErrorBoundary;
