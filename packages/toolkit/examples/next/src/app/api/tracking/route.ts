import { IClocSession } from '@/app/replay/types/api-types';
import { NextResponse } from 'next/server';

// In-memory temp DB for session replays - now stores sessions grouped by sessionId
const sessionDB: IClocSession[] = [];

// Helper function to find or create a session
function findOrCreateSession(
	sessionId: string,
	employeeId: string,
	organizationId: string,
	tenantId: string
): IClocSession {
	let session = sessionDB.find(
		(s) =>
			s.sessionId === sessionId &&
			s.employeeId === employeeId &&
			s.organizationId === organizationId &&
			s.tenantId === tenantId
	);

	if (!session) {
		session = {
			sessionId,
			payloads: [],
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
			employeeId,
			organizationId,
			tenantId
		};
		sessionDB.push(session);
	}

	return session;
}

// Add CORS headers to allow any origin and any method
function withCors(response: Response) {
	response.headers.set('Access-Control-Allow-Origin', '*');
	response.headers.set('Access-Control-Allow-Methods', 'GET,POST,OPTIONS');
	response.headers.set('Access-Control-Allow-Headers', '*');
	return response;
}

export async function OPTIONS() {
	const response = new Response(null, { status: 204 });
	return withCors(response);
}

export async function POST(request: Request) {
	try {
		const data: { payload: string; timestamp: string } = await request.json();
		if (!data?.payload) {
			return withCors(
				NextResponse.json(
					{
						success: false,
						error: 'Missing required field: payload is required'
					},
					{ status: 400 }
				)
			);
		}

		const organizationId = request.headers.get('organization-id') || '';
		const tenantId = request.headers.get('tenant-id') || '';
		const employeeId = '6aa62a33-fd99-4077-8ca8-e292de176e60'; // Will get employeeId from Token

		// Decode the payload to extract sessionId
		let sessionId: string;
		try {
			// Import decode function
			const { decode } = await import('clarity-decode');
			const decodedPayload = decode(data.payload);
			sessionId = decodedPayload?.envelope?.sessionId || `fallback-${Date.now()}`;
		} catch (decodeError) {
			console.warn('Failed to decode payload for sessionId extraction, using fallback:', decodeError);
			sessionId = `fallback-${Date.now()}`;
		}

		// Find or create session and add the payload
		const session = findOrCreateSession(sessionId, employeeId, organizationId, tenantId);

		// Add the new payload to the session
		session.payloads.push({
			payload: data.payload,
			timestamp: data.timestamp || new Date().toISOString()
		});

		// Update the session's updatedAt timestamp
		session.updatedAt = new Date().toISOString();

		return withCors(
			NextResponse.json({
				success: true,
				data: session
			})
		);
	} catch (error) {
		console.error('Error processing tracking events:', error);
		const message = error instanceof Error ? error.message : 'An unknown error occurred.';
		const errorStack = error instanceof Error ? error.stack : undefined;
		return withCors(
			NextResponse.json(
				{ error: 'Failed to process tracking events', details: message, errorStack },
				{ status: 500 }
			)
		);
	}
}

export async function GET() {
	return withCors(NextResponse.json({ success: true, data: sessionDB }));
}
