{
    "extends": "@cloc/typescript-config/base.json",
    "compilerOptions": {
        "module": "esnext",
        "target": "es5",
        "lib": ["es6", "dom", "es2016", "es2017"],
        "baseUrl": ".",
        "paths": {
            "@src/*": ["src/*"],
        },
        "outDir": "dist",
        "composite": true,
        "moduleResolution": "node",
        "rootDir": "./src",
        "declaration": true, // Ensures type declarations are generated
		"declarationDir": "dist/types", // Output directory for declarations
		"emitDeclarationOnly": true, // Prevents emitting JS files
		"sourceMap": false,
		"declarationMap": false,
    },
    "include":["src/**/*.ts"],
    "exclude": [ "node_modules", "build", "dist"]
}
