import { clarity } from 'clarity-js';
import { IClocConfig } from '../types';
import { createClocConfig } from './config';
import { validateConfig } from '../helpers/validate-config';

const tracker = {
	start: (config: IClocConfig) => {
		try {
			validateConfig(config);
			const DEFAULT_CONFIG = createClocConfig(config);
			clarity.start(DEFAULT_CONFIG);
		} catch (error) {
			throw new Error(`Failed to start tracker: ${error}`);
		}
	},
	stop: () => {
		clarity.stop();
	},
	pause: () => {
		clarity.pause();
	},
	resume: () => {
		clarity.resume();
	}
};

export { tracker };
