{"name": "@cloc/tracking", "version": "0.0.1", "license": "MIT", "description": "An analytics library that uses web page interactions to generate aggregated insights based on Microsoft Clarity", "main": "dist/tracker.js", "module": "dist/tracker.module.js", "unpkg": "dist/tracker.min.js", "types": "dist/index.d.ts", "type": "module", "keywords": ["tracker", "tracking", "cloc", "analytics"], "dependencies": {"clarity-js": "^0.8.19"}, "devDependencies": {"@rollup/plugin-commonjs": "^24.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-terser": "^0.4.0", "@rollup/plugin-typescript": "^12.1.1", "rollup-plugin-dts": "^6.2.1", "del-cli": "^5.0.0", "rollup": "^3.0.0", "tslint": "^6.1.3", "typescript": "^5.6.3"}, "scripts": {"build": "yarn build:clean && yarn build:types && yarn build:main", "build:main": "rollup -c", "build:types": "tsc -noEmit", "build:clean": "del-cli dist/* .rollup.cache tsconfig.tsbuildinfo", "tslint": "tslint --project ./", "tslint:fix": "tslint --fix --project ./ --force"}, "files": ["dist"], "publishConfig": {"access": "restricted"}}