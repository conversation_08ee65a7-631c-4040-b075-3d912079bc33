import { IServerError, ITeamTask, IUser, PaginationResponse } from '@cloc/types';
import qs from 'qs';
import { ApiCall } from '../fetch';

export const getMyTasks = async (
	user: IUser | null,
	token: string,
	projectId: string | null,
	organizationId: string
) => {
	try {
		if (!user) throw new Error('User is not authenticated');

		const { tenantId } = user;
		const employeeId = user.employee ? user.employee.id : undefined;

		const query = qs.stringify({
			where: {
				organizationId,
				tenantId,
				...(projectId ? { projectId } : {})
			}
		});

		const path = employeeId ? `/tasks/employee/${employeeId}?${query}` : `/tasks?${query}`;

		const response = await ApiCall<PaginationResponse<ITeamTask>>({
			path,
			method: 'GET',
			bearer_token: token,
			tenantId
		});

		if ('data' in response) return response.data;

		if ('error' in response || 'message' in response) return response;

		return { message: 'Unexpected API response format.' } as IServerError;
	} catch (error) {
		return { message: error instanceof Error ? error.message : 'Unknown error occurred' } as IServerError;
	}
};
