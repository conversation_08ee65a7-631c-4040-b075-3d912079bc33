import { IProject, IServerError, IUser, PaginationResponse } from '@cloc/types';
import qs from 'qs';
import { ApiCall } from '../fetch';

export const getOrganisationProjects = async (
	user: IUser | null,
	token: string,
	selectedClient: string | null,
	organizationId: string
) => {
	try {
		if (!user) throw new Error('User is not authenticated');

		const { tenantId } = user;
		const employeeId = user.employee ? user.employee.id : undefined;

		const queryAdmin = qs.stringify(
			{
				where: {
					organizationId,
					tenantId
				},
				organizationContactId: selectedClient,
				...(employeeId && { employeeId })
			},
			{ skipNulls: true }
		);

		const path = `/organization-projects/?${queryAdmin}`;

		const response = await ApiCall<PaginationResponse<IProject>>({
			path,
			method: 'GET',
			bearer_token: token,
			tenantId
		});

		if ('data' in response) return response.data;

		if ('error' in response || 'message' in response) return response;

		return { message: 'Unexpected error format.' } as IServerError;
	} catch (error) {
		return { message: error instanceof Error ? error.message : String(error) } as IServerError;
	}
};
