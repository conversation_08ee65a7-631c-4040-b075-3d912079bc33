name: Mobile Build, Deploy and Publish Apps Android

on:
  workflow_run:
    workflows: ['Release Apps']
    branches: [apps]
    types:
      - completed

concurrency:
  group: ${{ github.ref }}-${{ github.workflow }}
  cancel-in-progress: true

jobs:
  deploy:
    runs-on: ubuntu-latest

    environment: prod

    permissions:
      contents: read

    steps:
      - name: Check for EXPO_TOKEN
        run: |
          if [ -z "${{ secrets.EXPO_TOKEN }}" ]; then
            echo "You must provide an EXPO_TOKEN secret linked to this project's Expo account in this repo's secrets. Learn more: https://docs.expo.dev/eas-update/github-actions"
            exit 1
          fi

      - name: Check out Git repository
        uses: actions/checkout@v4
        with:
          repository: 'ever-co/ever-teams'
          ref: develop

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.11.1'
          cache: 'yarn'

      - name: Setup EAS
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: Decode Google Credentials
        run: |
          DECODED_GOOGLE_CREDENTIALS=$(echo '${{secrets.GOOGLE_CREDENTIALS}}' | base64 --decode)
          echo "DECODED_GOOGLE_CREDENTIALS=$DECODED_GOOGLE_CREDENTIALS" >> $GITHUB_ENV
          echo "::add-mask::$DECODED_GOOGLE_CREDENTIALS"
          ESCAPED_GOOGLE_CREDENTIALS=$(echo "$DECODED_GOOGLE_CREDENTIALS" | sed ':a;N;$!ba;s/\n/\\n/g' | sed 's/"/\\"/g')
          ESCAPED_GOOGLE_CREDENTIALS=$(echo $ESCAPED_GOOGLE_CREDENTIALS | sed 's/\\n/\\\\n/g')
          echo "ESCAPED_GOOGLE_CREDENTIALS=$ESCAPED_GOOGLE_CREDENTIALS" >> $GITHUB_ENV
          echo "::add-mask::$ESCAPED_GOOGLE_CREDENTIALS"

      - name: Install Packages
        run: |
          yarn install --frozen-lockfile

      - name: Build Mobile
        run: |
          yarn build:mobile

      - name: Generate app.json and replace placeholders
        run: |
          cd apps/mobile && yarn config:mobile
        env:
          EXPO_PROJECT_SLUG: ${{ secrets.EXPO_PROJECT_SLUG }}
          EXPO_PROJECT_NAME: ${{ secrets.EXPO_PROJECT_NAME }}
          EXPO_PROJECT_OWNER: ${{ secrets.EXPO_PROJECT_OWNER }}
          EXPO_PROJECT_ID: ${{ secrets.EXPO_PROJECT_ID }}
          EXPO_PROJECT_PACKAGE_NAME: ${{ secrets.EXPO_PROJECT_PACKAGE_NAME }}
          EXPO_PROJECT_IOS_BUNDLE_IDENTIFIER: ${{ secrets.EXPO_PROJECT_PACKAGE_NAME }}

      - name: Build on EAS
        run: cd apps/mobile && eas build --platform android --non-interactive

      - name: Publish update
        run: cd apps/mobile && eas update --auto --platform android --non-interactive

      - name: 'Authenticate to Google Cloud'
        uses: 'google-github-actions/auth@v1'
        with:
          credentials_json: ${{ env.DECODED_GOOGLE_CREDENTIALS }}

      # Install gcloud, `setup-gcloud` automatically picks up authentication from `auth`.
      - name: 'Set up Cloud SDK'
        uses: 'google-github-actions/setup-gcloud@v1'
        with:
          project_id: 'ever-cloc'

      - name: Upload to Play Store Console
        run: cd apps/mobile && eas submit --platform android --profile production --latest --non-interactive
