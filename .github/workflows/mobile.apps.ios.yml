name: Mobile Build, Deploy and Publish Apps iOS

on:
  workflow_run:
    workflows: ['Release Apps']
    branches: [apps]
    types:
      - completed

concurrency:
  group: ${{ github.ref }}-${{ github.workflow }}
  cancel-in-progress: true

jobs:
  deploy:
    runs-on: ubuntu-latest

    environment: prod

    permissions:
      contents: read

    steps:
      - name: Check for EXPO_TOKEN
        run: |
          if [ -z "${{ secrets.EXPO_TOKEN }}" ]; then
            echo "You must provide an EXPO_TOKEN secret linked to this project's Expo account in this repo's secrets. Learn more: https://docs.expo.dev/eas-update/github-actions"
            exit 1
          fi

      - name: Check out Git repository
        uses: actions/checkout@v4
        with:
          repository: 'ever-co/ever-teams'
          ref: develop

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.11.1'
          cache: 'yarn'

      - name: Setup EAS
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: Set Apple Store Credentials
        run: |
          echo "APPLE_ID=${{ secrets.APPLE_ID }}" >> $GITHUB_ENV
          echo "APPSTORE_ISSUER_ID=${{ secrets.APPSTORE_ISSUER_ID }}" >> $GITHUB_ENV
          echo "APPSTORE_API_KEY_ID=${{ secrets.APPSTORE_API_KEY_ID }}" >> $GITHUB_ENV

      - name: Replace Secrets in eas.json
        run: |
          sed -i 's/APPLE_ID_PLACEHOLDER/${{ secrets.APPLE_ID }}/' ./apps/mobile/eas.json
          sed -i 's/APPSTORE_ISSUER_ID_PLACEHOLDER/${{ secrets.APPSTORE_ISSUER_ID }}/' ./apps/mobile/eas.json
          sed -i 's/APPSTORE_API_KEY_ID_PLACEHOLDER/${{ secrets.APPSTORE_API_KEY_ID }}/' ./apps/mobile/eas.json

      - name: Create Apple API Key File
        run: |
          echo "${{ secrets.APPSTORE_API_PRIVATE_KEY }}" > ./apps/mobile/AuthKey_R9QZ5LP8NK.p8

      - name: Install Packages
        run: |
          yarn install --frozen-lockfile

      - name: Build Mobile
        run: |
          yarn build:mobile

      - name: Generate app.json and replace placeholders
        run: |
          cd apps/mobile && yarn config:mobile
        env:
          EXPO_PROJECT_SLUG: ${{ secrets.EXPO_PROJECT_SLUG }}
          EXPO_PROJECT_NAME: ${{ secrets.EXPO_PROJECT_NAME }}
          EXPO_PROJECT_OWNER: ${{ secrets.EXPO_PROJECT_OWNER }}
          EXPO_PROJECT_ID: ${{ secrets.EXPO_PROJECT_ID }}
          EXPO_PROJECT_PACKAGE_NAME: ${{ secrets.EXPO_PROJECT_PACKAGE_NAME }}
          EXPO_PROJECT_IOS_BUNDLE_IDENTIFIER: ${{ secrets.EXPO_PROJECT_PACKAGE_NAME }}

      - name: Build on EAS
        run: cd apps/mobile && eas build --platform ios --non-interactive

      - name: Publish update
        run: cd apps/mobile && eas update --auto --platform ios --non-interactive

      - name: Upload App build to App store
        run: cd apps/mobile && eas submit --platform ios --profile production --latest --non-interactive
