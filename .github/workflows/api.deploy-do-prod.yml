name: Deploy API to DigitalOcean Prod

on:
  push:
    branches: [main]

concurrency:
  group: ${{ github.ref }}-${{ github.workflow }}
  cancel-in-progress: true

jobs:
  deploy-api-prod:
    runs-on: buildjet-4vcpu-ubuntu-2204

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

      - name: Log in to DigitalOcean Container Registry with short-lived credentials
        run: doctl registry login --expiry-seconds 600

      - name: Save DigitalOcean kubeconfig with short-lived credentials
        run: doctl kubernetes cluster kubeconfig save --expiry-seconds 600 k8s-gauzy

      - name: Generate TLS Secrets for API Ingress
        run: |
          rm -f ${HOME}/ingress.api.crt ${HOME}/ingress.api.key
          echo ${{ secrets.INGRESS_CERT }} | base64 --decode > ${HOME}/ingress.api.crt
          echo ${{ secrets.INGRESS_CERT_KEY }} | base64 --decode > ${HOME}/ingress.api.key          
          kubectl create secret tls api.cloc.co-tls --save-config --dry-run=client --cert=${HOME}/ingress.api.crt --key=${HOME}/ingress.api.key -o yaml | kubectl --context do-sfo2-k8s-gauzy apply -f -

      - name: Apply k8s manifests changes in DigitalOcean k8s cluster (if any)
        run: |
          envsubst < $GITHUB_WORKSPACE/.deploy/k8s/k8s-manifest-api.prod.yaml | kubectl --context do-sfo2-k8s-gauzy apply -f -
        env:
          # below we are using GitHub secrets for both frontend and backend
          DB_TYPE: '${{ secrets.DB_TYPE }}'
          DB_URI: '${{ secrets.DB_URI }}'
          DB_HOST: '${{ secrets.DB_HOST }}'
          DB_USER: '${{ secrets.DB_USER }}'
          DB_PASS: '${{ secrets.DB_PASS }}'
          DB_NAME: '${{ secrets.DB_NAME }}'
          DB_PORT: '${{ secrets.DB_PORT }}'
          DB_CA_CERT: '${{ secrets.DB_CA_CERT }}'
          DB_SSL_MODE: '${{ secrets.DB_SSL_MODE }}'
          REDIS_ENABLED: '${{ secrets.REDIS_ENABLED }}'
          REDIS_URL: '${{ secrets.REDIS_URL }}'
          CLOUD_PROVIDER: 'DO'
          SENTRY_DSN: '${{ secrets.SENTRY_DSN }}'
          SENTRY_TRACES_SAMPLE_RATE: '${{ secrets.SENTRY_TRACES_SAMPLE_RATE }}'
          SENTRY_PROFILE_SAMPLE_RATE: '${{ secrets.SENTRY_PROFILE_SAMPLE_RATE }}'
          SENTRY_HTTP_TRACING_ENABLED: '${{ secrets.SENTRY_HTTP_TRACING_ENABLED }}'
          SENTRY_POSTGRES_TRACKING_ENABLED: '${{ secrets.SENTRY_POSTGRES_TRACKING_ENABLED }}'
          SENTRY_PROFILING_ENABLED: '${{ secrets.SENTRY_PROFILING_ENABLED }}'
          AWS_ACCESS_KEY_ID: '${{ secrets.AWS_ACCESS_KEY_ID }}'
          AWS_SECRET_ACCESS_KEY: '${{ secrets.AWS_SECRET_ACCESS_KEY }}'
          AWS_REGION: '${{ secrets.AWS_REGION }}'
          AWS_S3_BUCKET: '${{ secrets.AWS_S3_BUCKET }}'
          WASABI_ACCESS_KEY_ID: '${{ secrets.WASABI_ACCESS_KEY_ID }}'
          WASABI_SECRET_ACCESS_KEY: '${{ secrets.WASABI_SECRET_ACCESS_KEY }}'
          WASABI_REGION: '${{ secrets.WASABI_REGION }}'
          WASABI_SERVICE_URL: '${{ secrets.WASABI_SERVICE_URL }}'
          WASABI_S3_BUCKET: '${{ secrets.WASABI_S3_BUCKET }}'
          EXPRESS_SESSION_SECRET: '${{ secrets.EXPRESS_SESSION_SECRET }}'
          JWT_SECRET: '${{ secrets.JWT_SECRET }}'
          JWT_REFRESH_TOKEN_SECRET: '${{ secrets.JWT_REFRESH_TOKEN_SECRET }}'
          JWT_REFRESH_TOKEN_EXPIRATION_TIME: '${{ secrets.JWT_REFRESH_TOKEN_EXPIRATION_TIME }}'
          CLOUDINARY_API_KEY: '${{ secrets.CLOUDINARY_API_KEY }}'
          CLOUDINARY_API_SECRET: '${{ secrets.CLOUDINARY_API_SECRET }}'
          CLOUDINARY_CLOUD_NAME: '${{ secrets.CLOUDINARY_CLOUD_NAME }}'
          MAIL_FROM_ADDRESS: '${{ secrets.MAIL_FROM_ADDRESS }}'
          MAIL_HOST: '${{ secrets.MAIL_HOST }}'
          MAIL_PORT: '${{ secrets.MAIL_PORT }}'
          MAIL_USERNAME: '${{ secrets.MAIL_USERNAME }}'
          MAIL_PASSWORD: '${{ secrets.MAIL_PASSWORD }}'
          ALLOW_SUPER_ADMIN_ROLE: '${{ secrets.ALLOW_SUPER_ADMIN_ROLE }}'
          GOOGLE_CLIENT_ID: '${{ secrets.GOOGLE_CLIENT_ID }}'
          GOOGLE_CLIENT_SECRET: '${{ secrets.GOOGLE_CLIENT_SECRET }}'
          GOOGLE_CALLBACK_URL: '${{ secrets.GOOGLE_CALLBACK_URL }}'
          FACEBOOK_CLIENT_ID: '${{ secrets.FACEBOOK_CLIENT_ID }}'
          FACEBOOK_CLIENT_SECRET: '${{ secrets.FACEBOOK_CLIENT_SECRET }}'
          FACEBOOK_GRAPH_VERSION: '${{ secrets.FACEBOOK_GRAPH_VERSION }}'
          FACEBOOK_CALLBACK_URL: '${{ secrets.FACEBOOK_CALLBACK_URL }}'
          INTEGRATED_USER_DEFAULT_PASS: '${{ secrets.INTEGRATED_USER_DEFAULT_PASS }}'
          UPWORK_REDIRECT_URL: '${{ secrets.UPWORK_REDIRECT_URL }}'
          FILE_PROVIDER: '${{ secrets.FILE_PROVIDER }}'
          GAUZY_AI_GRAPHQL_ENDPOINT: '${{ secrets.GAUZY_AI_GRAPHQL_ENDPOINT }}'
          GAUZY_AI_REST_ENDPOINT: '${{ secrets.GAUZY_AI_REST_ENDPOINT }}'
          UNLEASH_APP_NAME: '${{ secrets.UNLEASH_APP_NAME }}'
          UNLEASH_API_URL: '${{ secrets.UNLEASH_API_URL }}'
          UNLEASH_INSTANCE_ID: '${{ secrets.UNLEASH_INSTANCE_ID }}'
          UNLEASH_REFRESH_INTERVAL: '${{ secrets.UNLEASH_REFRESH_INTERVAL }}'
          UNLEASH_METRICS_INTERVAL: '${{ secrets.UNLEASH_METRICS_INTERVAL }}'
          UNLEASH_API_KEY: '${{ secrets.UNLEASH_API_KEY }}'
          PM2_MACHINE_NAME: '${{ secrets.PM2_MACHINE_NAME }}'
          PM2_SECRET_KEY: '${{ secrets.PM2_SECRET_KEY }}'
          PM2_PUBLIC_KEY: '${{ secrets.PM2_PUBLIC_KEY }}'
          JITSU_SERVER_URL: '${{ secrets.JITSU_SERVER_URL }}'
          JITSU_SERVER_WRITE_KEY: '${{ secrets.JITSU_SERVER_WRITE_KEY }}'
          OTEL_ENABLED: '${{ secrets.OTEL_ENABLED }}'
          OTEL_EXPORTER_OTLP_TRACES_ENDPOINT: '${{ secrets.OTEL_EXPORTER_OTLP_TRACES_ENDPOINT }}'
          OTEL_EXPORTER_OTLP_HEADERS: '${{ secrets.OTEL_EXPORTER_OTLP_HEADERS }}'
          GAUZY_GITHUB_CLIENT_ID: '${{ secrets.GAUZY_GITHUB_CLIENT_ID }}'
          GAUZY_GITHUB_CLIENT_SECRET: '${{ secrets.GAUZY_GITHUB_CLIENT_SECRET }}'
          GAUZY_GITHUB_APP_PRIVATE_KEY: '${{ secrets.GAUZY_GITHUB_APP_PRIVATE_KEY }}'
          GAUZY_GITHUB_WEBHOOK_URL: '${{ secrets.GAUZY_GITHUB_WEBHOOK_URL }}'
          GAUZY_GITHUB_WEBHOOK_SECRET: '${{ secrets.GAUZY_GITHUB_WEBHOOK_SECRET }}'
          GAUZY_GITHUB_APP_NAME: '${{ secrets.GAUZY_GITHUB_APP_NAME }}'
          GAUZY_GITHUB_REDIRECT_URL: '${{ secrets.GAUZY_GITHUB_REDIRECT_URL }}'
          GAUZY_GITHUB_POST_INSTALL_URL: '${{ secrets.GAUZY_GITHUB_POST_INSTALL_URL }}'
          GAUZY_GITHUB_APP_ID: '${{ secrets.GAUZY_GITHUB_APP_ID }}'
          GAUZY_GITHUB_OAUTH_CLIENT_ID: '${{ secrets.GAUZY_GITHUB_OAUTH_CLIENT_ID }}'
          GAUZY_GITHUB_OAUTH_CLIENT_SECRET: '${{ secrets.GAUZY_GITHUB_OAUTH_CLIENT_SECRET }}'
          GAUZY_GITHUB_OAUTH_CALLBACK_URL: '${{ secrets.GAUZY_GITHUB_OAUTH_CALLBACK_URL }}'
          JITSU_BROWSER_URL: '${{ secrets.JITSU_BROWSER_URL }}'
          JITSU_BROWSER_WRITE_KEY: '${{ secrets.JITSU_BROWSER_WRITE_KEY }}'
          MAGIC_CODE_EXPIRATION_TIME: '${{ secrets.MAGIC_CODE_EXPIRATION_TIME }}'
          APP_NAME: '${{ secrets.APP_NAME }}'
          APP_LOGO: '${{ secrets.APP_LOGO }}'
          APP_SIGNATURE: '${{ secrets.APP_SIGNATURE }}'
          APP_LINK: '${{ secrets.APP_LINK }}'
          APP_EMAIL_CONFIRMATION_URL: '${{ secrets.APP_EMAIL_CONFIRMATION_URL }}'
          APP_MAGIC_SIGN_URL: '${{ secrets.APP_MAGIC_SIGN_URL }}'
          COMPANY_LINK: '${{ secrets.COMPANY_LINK }}'
          COMPANY_NAME: '${{ secrets.COMPANY_NAME }}'

      - name: Restart Pods to pick up :latest tag version
        run: |
          kubectl --context do-sfo2-k8s-gauzy rollout restart deployment/ever-cloc-prod-api
