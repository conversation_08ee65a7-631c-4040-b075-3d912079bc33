{"importSorter.generalConfiguration.sortOnBeforeSave": false, "importSorter.sortConfiguration.joinImportPaths": false, "cSpell.userWords": [], "cSpell.enabled": true, "typescript.tsdk": "node_modules/typescript/lib", "typescript.enablePromptUseWorkspaceTsdk": true, "npm.packageManager": "yarn", "prettier.trailingComma": "none", "prettier.singleQuote": true, "editor.formatOnSave": true, "eslint.format.enable": true, "editor.tabSize": 4, "files.insertFinalNewline": true, "files.trimFinalNewlines": true, "files.trimTrailingWhitespace": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports": "never", "source.sortMembers": "never", "organizeImports": "never"}, "vsicons.presets.angular": true, "deepscan.enable": true, "cSpell.words": ["cloc", "Cloc", "Codementor", "doctl", "Gitter", "Shadcn", "tailwindcss", "Turborepo"], "files.exclude": {"**/.git": true, "**/.DS_Store": true, "**/node_modules": false, "**/public/**/*.png": false, "**/public/**/*.jpg": false, "**/public/**/*.pdf": true}, "search.exclude": {"**/node_modules": true, "**/.idea": true, "**/.idea/**/*.{xml,iml}": true, "**/.iml": true, "**/.yarn": true, "**/yarn.lock": true}, "docwriter.style": "Auto-detect"}